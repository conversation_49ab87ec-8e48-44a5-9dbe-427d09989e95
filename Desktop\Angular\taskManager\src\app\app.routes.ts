import { Routes } from '@angular/router';
import { HomePage } from './pages/home/<USER>';
import { DashboardPage } from './pages/dashboard/dashboard.page';
import { TaskDetailPage } from './pages/task-detail/task-detail.page';

export const routes: Routes = [
    { path: '', component: HomePage },
    { path: 'dashboard', component: DashboardPage },
    { path: 'task/:id', component: TaskDetailPage },
    { path: '**', redirectTo: '' }
];
