{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/directionality.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/bidi.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, DOCUMENT, signal, EventEmitter, Injectable } from '@angular/core';\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-browser because the unit tests\n * themselves use things like `querySelector` in test code.\n *\n * This token is defined in a separate file from Directionality as a workaround for\n * https://github.com/angular/angular/issues/22559\n *\n * @docs-private\n */\nconst DIR_DOCUMENT = new InjectionToken('cdk-dir-doc', {\n  providedIn: 'root',\n  factory: DIR_DOCUMENT_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction DIR_DOCUMENT_FACTORY() {\n  return inject(DOCUMENT);\n}\n\n/** Regex that matches locales with an RTL script. Taken from `goog.i18n.bidi.isRtlLanguage`. */\nconst RTL_LOCALE_PATTERN = /^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;\n/** Resolves a string value to a specific direction. */\nfunction _resolveDirectionality(rawValue) {\n  const value = rawValue?.toLowerCase() || '';\n  if (value === 'auto' && typeof navigator !== 'undefined' && navigator?.language) {\n    return RTL_LOCALE_PATTERN.test(navigator.language) ? 'rtl' : 'ltr';\n  }\n  return value === 'rtl' ? 'rtl' : 'ltr';\n}\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\nclass Directionality {\n  /** The current 'ltr' or 'rtl' value. */\n  get value() {\n    return this.valueSignal();\n  }\n  /**\n   * The current 'ltr' or 'rtl' value.\n   */\n  valueSignal = signal('ltr', ...(ngDevMode ? [{\n    debugName: \"valueSignal\"\n  }] : []));\n  /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n  change = new EventEmitter();\n  constructor() {\n    const _document = inject(DIR_DOCUMENT, {\n      optional: true\n    });\n    if (_document) {\n      const bodyDir = _document.body ? _document.body.dir : null;\n      const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n      this.valueSignal.set(_resolveDirectionality(bodyDir || htmlDir || 'ltr'));\n    }\n  }\n  ngOnDestroy() {\n    this.change.complete();\n  }\n  static ɵfac = function Directionality_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Directionality)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Directionality,\n    factory: Directionality.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Directionality, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { DIR_DOCUMENT, Directionality, _resolveDirectionality };\n", "import { _resolveDirectionality, Directionality } from './directionality.mjs';\nexport { DIR_DOCUMENT } from './directionality.mjs';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, signal, Directive, Output, Input, NgModule } from '@angular/core';\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\nclass Dir {\n  /** Whether the `value` has been set to its initial value. */\n  _isInitialized = false;\n  /** Direction as passed in by the consumer. */\n  _rawDir;\n  /** Event emitted when the direction changes. */\n  change = new EventEmitter();\n  /** @docs-private */\n  get dir() {\n    return this.valueSignal();\n  }\n  set dir(value) {\n    const previousValue = this.valueSignal();\n    // Note: `_resolveDirectionality` resolves the language based on the browser's language,\n    // whereas the browser does it based on the content of the element. Since doing so based\n    // on the content can be expensive, for now we're doing the simpler matching.\n    this.valueSignal.set(_resolveDirectionality(value));\n    this._rawDir = value;\n    if (previousValue !== this.valueSignal() && this._isInitialized) {\n      this.change.emit(this.valueSignal());\n    }\n  }\n  /** Current layout direction of the element. */\n  get value() {\n    return this.dir;\n  }\n  valueSignal = signal('ltr', ...(ngDevMode ? [{\n    debugName: \"valueSignal\"\n  }] : []));\n  /** Initialize once default value has been set. */\n  ngAfterContentInit() {\n    this._isInitialized = true;\n  }\n  ngOnDestroy() {\n    this.change.complete();\n  }\n  static ɵfac = function Dir_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Dir)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: Dir,\n    selectors: [[\"\", \"dir\", \"\"]],\n    hostVars: 1,\n    hostBindings: function Dir_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"dir\", ctx._rawDir);\n      }\n    },\n    inputs: {\n      dir: \"dir\"\n    },\n    outputs: {\n      change: \"dirChange\"\n    },\n    exportAs: [\"dir\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: Directionality,\n      useExisting: Dir\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Dir, [{\n    type: Directive,\n    args: [{\n      selector: '[dir]',\n      providers: [{\n        provide: Directionality,\n        useExisting: Dir\n      }],\n      host: {\n        '[attr.dir]': '_rawDir'\n      },\n      exportAs: 'dir'\n    }]\n  }], null, {\n    change: [{\n      type: Output,\n      args: ['dirChange']\n    }],\n    dir: [{\n      type: Input\n    }]\n  });\n})();\nclass BidiModule {\n  static ɵfac = function BidiModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BidiModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: BidiModule,\n    imports: [Dir],\n    exports: [Dir]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BidiModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Dir],\n      exports: [Dir]\n    }]\n  }], null, null);\n})();\nexport { BidiModule, Dir, Directionality };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAkBA,IAAM,eAAe,IAAI,eAAe,eAAe;AAAA,EACrD,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,SAAS,uBAAuB;AAC9B,SAAO,OAAO,QAAQ;AACxB;AAGA,IAAM,qBAAqB;AAE3B,SAAS,uBAAuB,UAAU;AACxC,QAAM,QAAQ,UAAU,YAAY,KAAK;AACzC,MAAI,UAAU,UAAU,OAAO,cAAc,eAAe,WAAW,UAAU;AAC/E,WAAO,mBAAmB,KAAK,UAAU,QAAQ,IAAI,QAAQ;AAAA,EAC/D;AACA,SAAO,UAAU,QAAQ,QAAQ;AACnC;AAKA,IAAM,iBAAN,MAAM,gBAAe;AAAA;AAAA,EAEnB,IAAI,QAAQ;AACV,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,OAAO,OAAO,GAAI,YAAY,CAAC;AAAA,IAC3C,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA;AAAA,EAER,SAAS,IAAI,aAAa;AAAA,EAC1B,cAAc;AACZ,UAAM,YAAY,OAAO,cAAc;AAAA,MACrC,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,WAAW;AACb,YAAM,UAAU,UAAU,OAAO,UAAU,KAAK,MAAM;AACtD,YAAM,UAAU,UAAU,kBAAkB,UAAU,gBAAgB,MAAM;AAC5E,WAAK,YAAY,IAAI,uBAAuB,WAAW,WAAW,KAAK,CAAC;AAAA,IAC1E;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,SAAS;AAAA,EACvB;AAAA,EACA,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,gBAAe;AAAA,IACxB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;;;AC5EH,IAAM,MAAN,MAAM,KAAI;AAAA;AAAA,EAER,iBAAiB;AAAA;AAAA,EAEjB;AAAA;AAAA,EAEA,SAAS,IAAI,aAAa;AAAA;AAAA,EAE1B,IAAI,MAAM;AACR,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EACA,IAAI,IAAI,OAAO;AACb,UAAM,gBAAgB,KAAK,YAAY;AAIvC,SAAK,YAAY,IAAI,uBAAuB,KAAK,CAAC;AAClD,SAAK,UAAU;AACf,QAAI,kBAAkB,KAAK,YAAY,KAAK,KAAK,gBAAgB;AAC/D,WAAK,OAAO,KAAK,KAAK,YAAY,CAAC;AAAA,IACrC;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc,OAAO,OAAO,GAAI,YAAY,CAAC;AAAA,IAC3C,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA;AAAA,EAER,qBAAqB;AACnB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,SAAS;AAAA,EACvB;AAAA,EACA,OAAO,OAAO,SAAS,YAAY,mBAAmB;AACpD,WAAO,KAAK,qBAAqB,MAAK;AAAA,EACxC;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,OAAO,EAAE,CAAC;AAAA,IAC3B,UAAU;AAAA,IACV,cAAc,SAAS,iBAAiB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,OAAO,IAAI,OAAO;AAAA,MACnC;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,KAAK;AAAA,IACP;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAC,KAAK;AAAA,IAChB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,KAAK,CAAC;AAAA,IAC5E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,GAAG;AAAA,IACb,SAAS,CAAC,GAAG;AAAA,EACf,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,GAAG;AAAA,MACb,SAAS,CAAC,GAAG;AAAA,IACf,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}