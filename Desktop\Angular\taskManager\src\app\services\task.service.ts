import { Injectable, computed, effect, signal } from '@angular/core';
import { Task, TaskStatus } from '../models/task.model';

const STORAGE_KEY = 'tm_tasks_v1';

@Injectable({ providedIn: 'root' })
export class TaskService {
    private readonly _tasks = signal<Task[]>(this.load());
    readonly tasks = computed(() => this._tasks());

    constructor() {
        effect(() => localStorage.setItem(STORAGE_KEY, JSON.stringify(this._tasks())));
    }

    private load(): Task[] {
        try { return JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]'); }
        catch { return []; }
    }

    add(task: Task) { this._tasks.update(t => [task, ...t]); }
    remove(id: string) { this._tasks.update(t => t.filter(x => x.id !== id)); }

    setStatus(id: string, status: TaskStatus) {
        const now = new Date().toISOString();
        this._tasks.update(t => t.map(x => x.id === id ? { ...x, status, updatedAt: now } : x));
    }

    update(task: Task) {
        const now = new Date().toISOString();
        this._tasks.update(t => t.map(x => x.id === task.id ? { ...task, updatedAt: now } : x));
    }

    byId(id: string) { return this._tasks().find(x => x.id === id) ?? null; }
}
