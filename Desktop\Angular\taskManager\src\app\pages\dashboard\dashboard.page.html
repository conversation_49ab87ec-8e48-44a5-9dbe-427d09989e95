<div class="topbar">
  <div class="left">
    <h1>Task Dashboard</h1>
    <div class="stat-cards">
      <div class="mini"><div class="k">Total Tasks</div><div class="v">{{ totals().total }}</div></div>
      <div class="mini"><div class="k">Pending</div><div class="v">{{ totals().pending }}</div></div>
      <div class="mini"><div class="k">In Progress</div><div class="v">{{ totals().inProgress }}</div></div>
      <div class="mini"><div class="k">Completed</div><div class="v">{{ totals().completed }}</div></div>
    </div>
  </div>
  <a mat-stroked-button color="primary" routerLink="/">
    <mat-icon>home</mat-icon> Back to Home
  </a>
</div>

<section class="filters">
  <mat-form-field appearance="outline" class="q">
    <mat-label>Search tasks</mat-label>
    <input matInput placeholder="Search..." [ngModel]="q()" (ngModelChange)="q.set($event)">
  </mat-form-field>

  <mat-form-field appearance="outline">
    <mat-label>Status</mat-label>
    <mat-select [ngModel]="status()" (ngModelChange)="status.set($event)">
      <mat-option value="all">All Status</mat-option>
      <mat-option value="pending">Pending</mat-option>
      <mat-option value="in_progress">In Progress</mat-option>
      <mat-option value="completed">Completed</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="outline">
    <mat-label>Priority</mat-label>
    <mat-select [ngModel]="priority()" (ngModelChange)="priority.set($event)">
      <mat-option value="all">All Priorities</mat-option>
      <mat-option value="low">Low</mat-option>
      <mat-option value="medium">Medium</mat-option>
      <mat-option value="high">High</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="outline">
    <mat-label>Sort by</mat-label>
    <mat-select [ngModel]="sort()" (ngModelChange)="sort.set($event)">
      <mat-option value="due_asc">Due Date ↑</mat-option>
      <mat-option value="due_desc">Due Date ↓</mat-option>
      <mat-option value="title_asc">Title A→Z</mat-option>
    </mat-select>
  </mat-form-field>
</section>

<section class="list">
  @for (t of filtered(); track t.id) {
    <tm-task-card [task]="t" (delete)="remove($event)"></tm-task-card>
  }
  @if (filtered().length === 0) {
    <div class="empty">
      <mat-icon>inbox</mat-icon>
      <p>No tasks match your filters.</p>
    </div>
  }
</section>
