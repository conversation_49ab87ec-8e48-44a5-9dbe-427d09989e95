<div class="header">
  <h1>Task Manager</h1>
  <a mat-stroked-button color="primary" routerLink="/dashboard">
    <mat-icon>list</mat-icon> View Dashboard
  </a>
</div>

<div class="status-cards">
  <div class="stat">
    <div class="top"><span>Tasks Today</span><button mat-icon-button><mat-icon>add</mat-icon></button></div>
    <div class="num">{{ todayCount() }}</div>
  </div>
  <div class="stat">
    <div class="top"><span>In Progress</span><button mat-icon-button><mat-icon>priority_high</mat-icon></button></div>
    <div class="num">{{ inProgress() }}</div>
  </div>
  <div class="stat">
    <div class="top"><span>Completed</span><button mat-icon-button><mat-icon>check_circle</mat-icon></button></div>
    <div class="num">{{ completed() }}</div>
  </div>
</div>

<section class="create">
  <h2>Create New Task</h2>

  <form [formGroup]="form" (ngSubmit)="submit()" novalidate class="form">
    <div class="row">
      <mat-form-field appearance="fill" class="w">
        <mat-label>Task Title *</mat-label>
        <input matInput formControlName="title" placeholder="Enter task title...">
        @if (form.controls.title.touched && form.controls.title.errors?.['required']) {
          <mat-error>Title is required</mat-error>
        }
        @if (form.controls.title.touched && form.controls.title.errors?.['minlength']) {
          <mat-error>At least 3 characters</mat-error>
        }
      </mat-form-field>

      <mat-form-field appearance="fill" class="w">
        <mat-label>Due Date *</mat-label>
        <input matInput [matDatepicker]="picker" formControlName="dueDate" placeholder="dd-mm-yyyy">
        <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
        @if (form.controls.dueDate.touched && form.controls.dueDate.errors?.['required']) {
          <mat-error>Due date is required</mat-error>
        }
        @if (form.controls.dueDate.touched && form.controls.dueDate.errors?.['pastDate']) {
          <mat-error>Date cannot be in the past</mat-error>
        }
      </mat-form-field>
    </div>

    <mat-form-field appearance="fill" class="block">
      <mat-label>Description</mat-label>
      <textarea matInput rows="3" formControlName="description" placeholder="Add task description..."></textarea>
    </mat-form-field>

    <mat-form-field appearance="fill" class="block">
      <mat-label>Priority</mat-label>
      <mat-select formControlName="priority">
        <mat-option value="low">Low Priority</mat-option>
        <mat-option value="medium">Medium Priority</mat-option>
        <mat-option value="high">High Priority</mat-option>
      </mat-select>
    </mat-form-field>

    <div class="actions">
      <button mat-flat-button color="primary" type="submit">
        <mat-icon>add_task</mat-icon> Create Task
      </button>
      <a mat-stroked-button routerLink="/dashboard">
        <mat-icon>view_list</mat-icon> View All Tasks
      </a>
    </div>
  </form>
</section>
