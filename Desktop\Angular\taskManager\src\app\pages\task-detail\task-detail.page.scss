:host {
    display: block;
    padding: 24px;
    background: #f7f8fb;
}

.header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;

    .titles {
        flex: 1;

        h1 {
            margin: 0;
        }

        p {
            margin: 2px 0 0;
            color: #6b7280;
        }
    }

    .header-actions {
        display: flex;
        gap: 8px;
    }
}

.grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 16px;
}

.panel {
    background: #fff;
    border-radius: 16px;
    padding: 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, .06);
}

.read {
    .title {
        margin: 0 0 6px;
    }

    .chips {
        display: flex;
        gap: 8px;
        margin-bottom: 12px;

        .chip {
            padding: 2px 10px;
            border-radius: 999px;
            font-size: 12px;
            text-transform: capitalize;
        }

        .priority.low {
            background: #edf7ff
        }

        .priority.medium {
            background: #f5f2ff
        }

        .priority.high {
            background: #ffebee
        }

        .status.pending {
            background: #fff7e6
        }

        .status.in {
            background: #e8f5ff
        }

        .status.done {
            background: #eafbea
        }
    }

    .block {
        margin: 12px 0;

        label {
            display: block;
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 6px;
        }

        .desc {
            background: #f3f4f6;
            border-radius: 10px;
            padding: 12px;
        }
    }

    .meta-line {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #6b7280;
    }
}

.side .panel+.panel {
    margin-top: 16px;
}

.stack {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.meta {
    display: flex;
    flex-direction: column;
    gap: 10px;

    span {
        color: #6b7280;
        font-size: 12px;
    }

    b {
        font-weight: 600;
    }
}

.edit {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .row {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
    }

    .w {
        width: 100%;
    }

    .actions {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
    }
}

.danger {
    border: 1px solid #fecaca;
    background: #fff5f5;

    h3 {
        margin-top: 0;
    }

    .dz {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 12px;

        p {
            margin: 2px 0 0;
            color: #6b7280;
        }
    }
}

.empty {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 12px;
    color: #6b7280;
}