{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/unique-selection-dispatcher.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/selection-model.mjs", "../../../../../../node_modules/@angular/material/fesm2022/select-module.mjs", "../../../../../../node_modules/@angular/material/fesm2022/select.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\nclass UniqueSelectionDispatcher {\n  _listeners = [];\n  /**\n   * Notify other items that selection for the given name has been set.\n   * @param id ID of the item.\n   * @param name Name of the item.\n   */\n  notify(id, name) {\n    for (let listener of this._listeners) {\n      listener(id, name);\n    }\n  }\n  /**\n   * Listen for future changes to item selection.\n   * @return Function used to deregister listener\n   */\n  listen(listener) {\n    this._listeners.push(listener);\n    return () => {\n      this._listeners = this._listeners.filter(registered => {\n        return listener !== registered;\n      });\n    };\n  }\n  ngOnDestroy() {\n    this._listeners = [];\n  }\n  static ɵfac = function UniqueSelectionDispatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || UniqueSelectionDispatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: UniqueSelectionDispatcher,\n    factory: UniqueSelectionDispatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UniqueSelectionDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { UniqueSelectionDispatcher };\n", "import { Subject } from 'rxjs';\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nclass SelectionModel {\n    _multiple;\n    _emitChanges;\n    compareWith;\n    /** Currently-selected values. */\n    _selection = new Set();\n    /** Keeps track of the deselected options that haven't been emitted by the change event. */\n    _deselectedToEmit = [];\n    /** Keeps track of the selected options that haven't been emitted by the change event. */\n    _selectedToEmit = [];\n    /** Cache for the array value of the selected items. */\n    _selected;\n    /** Selected values. */\n    get selected() {\n        if (!this._selected) {\n            this._selected = Array.from(this._selection.values());\n        }\n        return this._selected;\n    }\n    /** Event emitted when the value has changed. */\n    changed = new Subject();\n    constructor(_multiple = false, initiallySelectedValues, _emitChanges = true, compareWith) {\n        this._multiple = _multiple;\n        this._emitChanges = _emitChanges;\n        this.compareWith = compareWith;\n        if (initiallySelectedValues && initiallySelectedValues.length) {\n            if (_multiple) {\n                initiallySelectedValues.forEach(value => this._markSelected(value));\n            }\n            else {\n                this._markSelected(initiallySelectedValues[0]);\n            }\n            // Clear the array in order to avoid firing the change event for preselected values.\n            this._selectedToEmit.length = 0;\n        }\n    }\n    /**\n     * Selects a value or an array of values.\n     * @param values The values to select\n     * @return Whether the selection changed as a result of this call\n     */\n    select(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._markSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Deselects a value or an array of values.\n     * @param values The values to deselect\n     * @return Whether the selection changed as a result of this call\n     */\n    deselect(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._unmarkSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Sets the selected values\n     * @param values The new selected values\n     * @return Whether the selection changed as a result of this call\n     */\n    setSelection(...values) {\n        this._verifyValueAssignment(values);\n        const oldValues = this.selected;\n        const newSelectedSet = new Set(values.map(value => this._getConcreteValue(value)));\n        values.forEach(value => this._markSelected(value));\n        oldValues\n            .filter(value => !newSelectedSet.has(this._getConcreteValue(value, newSelectedSet)))\n            .forEach(value => this._unmarkSelected(value));\n        const changed = this._hasQueuedChanges();\n        this._emitChangeEvent();\n        return changed;\n    }\n    /**\n     * Toggles a value between selected and deselected.\n     * @param value The value to toggle\n     * @return Whether the selection changed as a result of this call\n     */\n    toggle(value) {\n        return this.isSelected(value) ? this.deselect(value) : this.select(value);\n    }\n    /**\n     * Clears all of the selected values.\n     * @param flushEvent Whether to flush the changes in an event.\n     *   If false, the changes to the selection will be flushed along with the next event.\n     * @return Whether the selection changed as a result of this call\n     */\n    clear(flushEvent = true) {\n        this._unmarkAll();\n        const changed = this._hasQueuedChanges();\n        if (flushEvent) {\n            this._emitChangeEvent();\n        }\n        return changed;\n    }\n    /**\n     * Determines whether a value is selected.\n     */\n    isSelected(value) {\n        return this._selection.has(this._getConcreteValue(value));\n    }\n    /**\n     * Determines whether the model does not have a value.\n     */\n    isEmpty() {\n        return this._selection.size === 0;\n    }\n    /**\n     * Determines whether the model has a value.\n     */\n    hasValue() {\n        return !this.isEmpty();\n    }\n    /**\n     * Sorts the selected values based on a predicate function.\n     */\n    sort(predicate) {\n        if (this._multiple && this.selected) {\n            this._selected.sort(predicate);\n        }\n    }\n    /**\n     * Gets whether multiple values can be selected.\n     */\n    isMultipleSelection() {\n        return this._multiple;\n    }\n    /** Emits a change event and clears the records of selected and deselected values. */\n    _emitChangeEvent() {\n        // Clear the selected values so they can be re-cached.\n        this._selected = null;\n        if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n            this.changed.next({\n                source: this,\n                added: this._selectedToEmit,\n                removed: this._deselectedToEmit,\n            });\n            this._deselectedToEmit = [];\n            this._selectedToEmit = [];\n        }\n    }\n    /** Selects a value. */\n    _markSelected(value) {\n        value = this._getConcreteValue(value);\n        if (!this.isSelected(value)) {\n            if (!this._multiple) {\n                this._unmarkAll();\n            }\n            if (!this.isSelected(value)) {\n                this._selection.add(value);\n            }\n            if (this._emitChanges) {\n                this._selectedToEmit.push(value);\n            }\n        }\n    }\n    /** Deselects a value. */\n    _unmarkSelected(value) {\n        value = this._getConcreteValue(value);\n        if (this.isSelected(value)) {\n            this._selection.delete(value);\n            if (this._emitChanges) {\n                this._deselectedToEmit.push(value);\n            }\n        }\n    }\n    /** Clears out the selected values. */\n    _unmarkAll() {\n        if (!this.isEmpty()) {\n            this._selection.forEach(value => this._unmarkSelected(value));\n        }\n    }\n    /**\n     * Verifies the value assignment and throws an error if the specified value array is\n     * including multiple values while the selection model is not supporting multiple values.\n     */\n    _verifyValueAssignment(values) {\n        if (values.length > 1 && !this._multiple && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMultipleValuesInSingleSelectionError();\n        }\n    }\n    /** Whether there are queued up change to be emitted. */\n    _hasQueuedChanges() {\n        return !!(this._deselectedToEmit.length || this._selectedToEmit.length);\n    }\n    /** Returns a value that is comparable to inputValue by applying compareWith function, returns the same inputValue otherwise. */\n    _getConcreteValue(inputValue, selection) {\n        if (!this.compareWith) {\n            return inputValue;\n        }\n        else {\n            selection = selection ?? this._selection;\n            for (let selectedValue of selection) {\n                if (this.compareWith(inputValue, selectedValue)) {\n                    return selectedValue;\n                }\n            }\n            return inputValue;\n        }\n    }\n}\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n * @docs-private\n */\nfunction getMultipleValuesInSingleSelectionError() {\n    return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\n\nexport { SelectionModel, getMultipleValuesInSingleSelectionError };\n\n", "import { createRepositionScrollStrategy, CdkConnectedOverlay, CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, Injector, ChangeDetectorRef, ElementRef, Renderer2, signal, EventEmitter, HostAttributeToken, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, ContentChild, Input, ViewChild, Output, Directive, NgModule } from '@angular/core';\nimport { ViewportRuler, CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { _IdGenerator, LiveAnnouncer, removeAriaReferencedId, addAriaReferencedId, ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { hasModifier<PERSON><PERSON>, ENTER, SPACE, A, ESCAPE, DOWN_ARROW, UP_ARROW, LEFT_ARROW, RIGHT_ARROW } from '@angular/cdk/keycodes';\nimport { NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { Subject, defer, merge } from 'rxjs';\nimport { startWith, switchMap, filter, map, takeUntil, take } from 'rxjs/operators';\nimport { NgClass } from '@angular/common';\nimport { MAT_FORM_FIELD, MatFormFieldControl } from './form-field2.mjs';\nimport { _animationsDisabled } from './animation.mjs';\nimport { _countGroupLabelsBeforeOption, _getOptionScrollPosition, MAT_OPTION_PARENT_COMPONENT, MatOption, MAT_OPTGROUP } from './option.mjs';\nimport { ErrorStateMatcher } from './error-options.mjs';\nimport { _ErrorStateTracker } from './error-state.mjs';\nimport { MatOptionModule } from './option-module.mjs';\nimport { MatCommonModule } from './common-module.mjs';\nimport { MatFormFieldModule } from './form-field-module.mjs';\n\n// Note that these have been copied over verbatim from\n// `material/select` so that we don't have to expose them publicly.\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\nconst _c0 = [\"trigger\"];\nconst _c1 = [\"panel\"];\nconst _c2 = [[[\"mat-select-trigger\"]], \"*\"];\nconst _c3 = [\"mat-select-trigger\", \"*\"];\nfunction MatSelect_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.placeholder);\n  }\n}\nfunction MatSelect_Conditional_5_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction MatSelect_Conditional_5_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.triggerValue);\n  }\n}\nfunction MatSelect_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵconditionalCreate(1, MatSelect_Conditional_5_Conditional_1_Template, 1, 0)(2, MatSelect_Conditional_5_Conditional_2_Template, 2, 1, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.customTrigger ? 1 : 2);\n  }\n}\nfunction MatSelect_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12, 1);\n    i0.ɵɵlistener(\"keydown\", function MatSelect_ng_template_10_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._handleKeydown($event));\n    });\n    i0.ɵɵprojection(2, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(i0.ɵɵinterpolate1(\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open \", ctx_r1._getPanelTheme()));\n    i0.ɵɵclassProp(\"mat-select-panel-animations-enabled\", !ctx_r1._animationsDisabled);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.panelClass);\n    i0.ɵɵattribute(\"id\", ctx_r1.id + \"-panel\")(\"aria-multiselectable\", ctx_r1.multiple)(\"aria-label\", ctx_r1.ariaLabel || null)(\"aria-labelledby\", ctx_r1._getPanelAriaLabelledby());\n  }\n}\nfunction getMatSelectDynamicMultipleError() {\n  return Error('Cannot change `multiple` mode of select after initialization.');\n}\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\nfunction getMatSelectNonArrayValueError() {\n  return Error('Value must be an array in multiple-selection mode.');\n}\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\nfunction getMatSelectNonFunctionValueError() {\n  return Error('`compareWith` must be a function.');\n}\n\n/** Injection token that determines the scroll handling while a select is open. */\nconst MAT_SELECT_SCROLL_STRATEGY = new InjectionToken('mat-select-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const injector = inject(Injector);\n    return () => createRepositionScrollStrategy(injector);\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(_overlay) {\n  const injector = inject(Injector);\n  return () => createRepositionScrollStrategy(injector);\n}\n/** Injection token that can be used to provide the default options the select module. */\nconst MAT_SELECT_CONFIG = new InjectionToken('MAT_SELECT_CONFIG');\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_SELECT_SCROLL_STRATEGY,\n  deps: [],\n  useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SELECT_TRIGGER = new InjectionToken('MatSelectTrigger');\n/** Change event object that is emitted when the select value has changed. */\nclass MatSelectChange {\n  source;\n  value;\n  constructor(/** Reference to the select that emitted the change event. */\n  source, /** Current value of the select that emitted the event. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\nclass MatSelect {\n  _viewportRuler = inject(ViewportRuler);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _elementRef = inject(ElementRef);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _idGenerator = inject(_IdGenerator);\n  _renderer = inject(Renderer2);\n  _parentFormField = inject(MAT_FORM_FIELD, {\n    optional: true\n  });\n  ngControl = inject(NgControl, {\n    self: true,\n    optional: true\n  });\n  _liveAnnouncer = inject(LiveAnnouncer);\n  _defaultOptions = inject(MAT_SELECT_CONFIG, {\n    optional: true\n  });\n  _animationsDisabled = _animationsDisabled();\n  _initialized = new Subject();\n  _cleanupDetach;\n  /** All of the defined select options. */\n  options;\n  // TODO(crisbeto): this is only necessary for the non-MDC select, but it's technically a\n  // public API so we have to keep it. It should be deprecated and removed eventually.\n  /** All of the defined groups of options. */\n  optionGroups;\n  /** User-supplied override of the trigger element. */\n  customTrigger;\n  /**\n   * This position config ensures that the top \"start\" corner of the overlay\n   * is aligned with with the top \"start\" of the origin by default (overlapping\n   * the trigger completely). If the panel cannot fit below the trigger, it\n   * will fall back to a position above the trigger.\n   */\n  _positions = [{\n    originX: 'start',\n    originY: 'bottom',\n    overlayX: 'start',\n    overlayY: 'top'\n  }, {\n    originX: 'end',\n    originY: 'bottom',\n    overlayX: 'end',\n    overlayY: 'top'\n  }, {\n    originX: 'start',\n    originY: 'top',\n    overlayX: 'start',\n    overlayY: 'bottom',\n    panelClass: 'mat-mdc-select-panel-above'\n  }, {\n    originX: 'end',\n    originY: 'top',\n    overlayX: 'end',\n    overlayY: 'bottom',\n    panelClass: 'mat-mdc-select-panel-above'\n  }];\n  /** Scrolls a particular option into the view. */\n  _scrollOptionIntoView(index) {\n    const option = this.options.toArray()[index];\n    if (option) {\n      const panel = this.panel.nativeElement;\n      const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n      const element = option._getHostElement();\n      if (index === 0 && labelCount === 1) {\n        // If we've got one group label before the option and we're at the top option,\n        // scroll the list to the top. This is better UX than scrolling the list to the\n        // top of the option, because it allows the user to read the top group's label.\n        panel.scrollTop = 0;\n      } else {\n        panel.scrollTop = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, panel.scrollTop, panel.offsetHeight);\n      }\n    }\n  }\n  /** Called when the panel has been opened and the overlay has settled on its final position. */\n  _positioningSettled() {\n    this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n  }\n  /** Creates a change event object that should be emitted by the select. */\n  _getChangeEvent(value) {\n    return new MatSelectChange(this, value);\n  }\n  /** Factory function used to create a scroll strategy for this select. */\n  _scrollStrategyFactory = inject(MAT_SELECT_SCROLL_STRATEGY);\n  /** Whether or not the overlay panel is open. */\n  _panelOpen = false;\n  /** Comparison function to specify which option is displayed. Defaults to object equality. */\n  _compareWith = (o1, o2) => o1 === o2;\n  /** Unique id for this input. */\n  _uid = this._idGenerator.getId('mat-select-');\n  /** Current `aria-labelledby` value for the select trigger. */\n  _triggerAriaLabelledBy = null;\n  /**\n   * Keeps track of the previous form control assigned to the select.\n   * Used to detect if it has changed.\n   */\n  _previousControl;\n  /** Emits whenever the component is destroyed. */\n  _destroy = new Subject();\n  /** Tracks the error state of the select. */\n  _errorStateTracker;\n  /**\n   * Emits whenever the component state changes and should cause the parent\n   * form-field to update. Implemented as part of `MatFormFieldControl`.\n   * @docs-private\n   */\n  stateChanges = new Subject();\n  /**\n   * Disable the automatic labeling to avoid issues like #27241.\n   * @docs-private\n   */\n  disableAutomaticLabeling = true;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  userAriaDescribedBy;\n  /** Deals with the selection logic. */\n  _selectionModel;\n  /** Manages keyboard events for options in the panel. */\n  _keyManager;\n  /** Ideal origin for the overlay panel. */\n  _preferredOverlayOrigin;\n  /** Width of the overlay panel. */\n  _overlayWidth;\n  /** `View -> model callback called when value changes` */\n  _onChange = () => {};\n  /** `View -> model callback called when select has been touched` */\n  _onTouched = () => {};\n  /** ID for the DOM node containing the select's value. */\n  _valueId = this._idGenerator.getId('mat-select-value-');\n  /** Strategy that will be used to handle scrolling while the select panel is open. */\n  _scrollStrategy;\n  _overlayPanelClass = this._defaultOptions?.overlayPanelClass || '';\n  /** Whether the select is focused. */\n  get focused() {\n    return this._focused || this._panelOpen;\n  }\n  _focused = false;\n  /** A name for this control that can be used by `mat-form-field`. */\n  controlType = 'mat-select';\n  /** Trigger that opens the select. */\n  trigger;\n  /** Panel containing the select options. */\n  panel;\n  /** Overlay pane containing the options. */\n  _overlayDir;\n  /** Classes to be passed to the select panel. Supports the same syntax as `ngClass`. */\n  panelClass;\n  /** Whether the select is disabled. */\n  disabled = false;\n  /** Whether ripples in the select are disabled. */\n  get disableRipple() {\n    return this._disableRipple();\n  }\n  set disableRipple(value) {\n    this._disableRipple.set(value);\n  }\n  _disableRipple = signal(false, ...(ngDevMode ? [{\n    debugName: \"_disableRipple\"\n  }] : []));\n  /** Tab index of the select. */\n  tabIndex = 0;\n  /** Whether checkmark indicator for single-selection options is hidden. */\n  get hideSingleSelectionIndicator() {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value) {\n    this._hideSingleSelectionIndicator = value;\n    this._syncParentProperties();\n  }\n  _hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n  /** Placeholder to be shown if no value has been selected. */\n  get placeholder() {\n    return this._placeholder;\n  }\n  set placeholder(value) {\n    this._placeholder = value;\n    this.stateChanges.next();\n  }\n  _placeholder;\n  /** Whether the component is required. */\n  get required() {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n  set required(value) {\n    this._required = value;\n    this.stateChanges.next();\n  }\n  _required;\n  /** Whether the user should be allowed to select multiple options. */\n  get multiple() {\n    return this._multiple;\n  }\n  set multiple(value) {\n    if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectDynamicMultipleError();\n    }\n    this._multiple = value;\n  }\n  _multiple = false;\n  /** Whether to center the active option over the trigger. */\n  disableOptionCentering = this._defaultOptions?.disableOptionCentering ?? false;\n  /**\n   * Function to compare the option values with the selected values. The first argument\n   * is a value from an option. The second is a value from the selection. A boolean\n   * should be returned.\n   */\n  get compareWith() {\n    return this._compareWith;\n  }\n  set compareWith(fn) {\n    if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectNonFunctionValueError();\n    }\n    this._compareWith = fn;\n    if (this._selectionModel) {\n      // A different comparator means the selection could change.\n      this._initializeSelection();\n    }\n  }\n  /** Value of the select control. */\n  get value() {\n    return this._value;\n  }\n  set value(newValue) {\n    const hasAssigned = this._assignValue(newValue);\n    if (hasAssigned) {\n      this._onChange(newValue);\n    }\n  }\n  _value;\n  /** Aria label of the select. */\n  ariaLabel = '';\n  /** Input that can be used to specify the `aria-labelledby` attribute. */\n  ariaLabelledby;\n  /** Object used to control when error messages are shown. */\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value) {\n    this._errorStateTracker.matcher = value;\n  }\n  /** Time to wait in milliseconds after the last keystroke before moving focus to an item. */\n  typeaheadDebounceInterval;\n  /**\n   * Function used to sort the values in a select in multiple mode.\n   * Follows the same logic as `Array.prototype.sort`.\n   */\n  sortComparator;\n  /** Unique id of the element. */\n  get id() {\n    return this._id;\n  }\n  set id(value) {\n    this._id = value || this._uid;\n    this.stateChanges.next();\n  }\n  _id;\n  /** Whether the select is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value) {\n    this._errorStateTracker.errorState = value;\n  }\n  /**\n   * Width of the panel. If set to `auto`, the panel will match the trigger width.\n   * If set to null or an empty string, the panel will grow to match the longest option's text.\n   */\n  panelWidth = this._defaultOptions && typeof this._defaultOptions.panelWidth !== 'undefined' ? this._defaultOptions.panelWidth : 'auto';\n  /**\n   * By default selecting an option with a `null` or `undefined` value will reset the select's\n   * value. Enable this option if the reset behavior doesn't match your requirements and instead\n   * the nullable options should become selected. The value of this input can be controlled app-wide\n   * using the `MAT_SELECT_CONFIG` injection token.\n   */\n  canSelectNullableOptions = this._defaultOptions?.canSelectNullableOptions ?? false;\n  /** Combined stream of all of the child options' change events. */\n  optionSelectionChanges = defer(() => {\n    const options = this.options;\n    if (options) {\n      return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n    }\n    return this._initialized.pipe(switchMap(() => this.optionSelectionChanges));\n  });\n  /** Event emitted when the select panel has been toggled. */\n  openedChange = new EventEmitter();\n  /** Event emitted when the select has been opened. */\n  _openedStream = this.openedChange.pipe(filter(o => o), map(() => {}));\n  /** Event emitted when the select has been closed. */\n  _closedStream = this.openedChange.pipe(filter(o => !o), map(() => {}));\n  /** Event emitted when the selected value has been changed by the user. */\n  selectionChange = new EventEmitter();\n  /**\n   * Event that emits whenever the raw value of the select changes. This is here primarily\n   * to facilitate the two-way binding for the `value` input.\n   * @docs-private\n   */\n  valueChange = new EventEmitter();\n  constructor() {\n    const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n    const parentForm = inject(NgForm, {\n      optional: true\n    });\n    const parentFormGroup = inject(FormGroupDirective, {\n      optional: true\n    });\n    const tabIndex = inject(new HostAttributeToken('tabindex'), {\n      optional: true\n    });\n    if (this.ngControl) {\n      // Note: we provide the value accessor through here, instead of\n      // the `providers` to avoid running into a circular import.\n      this.ngControl.valueAccessor = this;\n    }\n    // Note that we only want to set this when the defaults pass it in, otherwise it should\n    // stay as `undefined` so that it falls back to the default in the key manager.\n    if (this._defaultOptions?.typeaheadDebounceInterval != null) {\n      this.typeaheadDebounceInterval = this._defaultOptions.typeaheadDebounceInterval;\n    }\n    this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n    this._scrollStrategy = this._scrollStrategyFactory();\n    this.tabIndex = tabIndex == null ? 0 : parseInt(tabIndex) || 0;\n    // Force setter to be called in case id was not specified.\n    this.id = this.id;\n  }\n  ngOnInit() {\n    this._selectionModel = new SelectionModel(this.multiple);\n    this.stateChanges.next();\n    this._viewportRuler.change().pipe(takeUntil(this._destroy)).subscribe(() => {\n      if (this.panelOpen) {\n        this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n        this._changeDetectorRef.detectChanges();\n      }\n    });\n  }\n  ngAfterContentInit() {\n    this._initialized.next();\n    this._initialized.complete();\n    this._initKeyManager();\n    this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n      event.added.forEach(option => option.select());\n      event.removed.forEach(option => option.deselect());\n    });\n    this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n      this._resetOptions();\n      this._initializeSelection();\n    });\n  }\n  ngDoCheck() {\n    const newAriaLabelledby = this._getTriggerAriaLabelledby();\n    const ngControl = this.ngControl;\n    // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n    // is computed as a result of a content query which can cause this binding to trigger a\n    // \"changed after checked\" error.\n    if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n      const element = this._elementRef.nativeElement;\n      this._triggerAriaLabelledBy = newAriaLabelledby;\n      if (newAriaLabelledby) {\n        element.setAttribute('aria-labelledby', newAriaLabelledby);\n      } else {\n        element.removeAttribute('aria-labelledby');\n      }\n    }\n    if (ngControl) {\n      // The disabled state might go out of sync if the form group is swapped out. See #17860.\n      if (this._previousControl !== ngControl.control) {\n        if (this._previousControl !== undefined && ngControl.disabled !== null && ngControl.disabled !== this.disabled) {\n          this.disabled = ngControl.disabled;\n        }\n        this._previousControl = ngControl.control;\n      }\n      this.updateErrorState();\n    }\n  }\n  ngOnChanges(changes) {\n    // Updating the disabled state is handled by the input, but we need to additionally let\n    // the parent form field know to run change detection when the disabled state changes.\n    if (changes['disabled'] || changes['userAriaDescribedBy']) {\n      this.stateChanges.next();\n    }\n    if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n      this._keyManager.withTypeAhead(this.typeaheadDebounceInterval);\n    }\n  }\n  ngOnDestroy() {\n    this._cleanupDetach?.();\n    this._keyManager?.destroy();\n    this._destroy.next();\n    this._destroy.complete();\n    this.stateChanges.complete();\n    this._clearFromModal();\n  }\n  /** Toggles the overlay panel open or closed. */\n  toggle() {\n    this.panelOpen ? this.close() : this.open();\n  }\n  /** Opens the overlay panel. */\n  open() {\n    if (!this._canOpen()) {\n      return;\n    }\n    // It's important that we read this as late as possible, because doing so earlier will\n    // return a different element since it's based on queries in the form field which may\n    // not have run yet. Also this needs to be assigned before we measure the overlay width.\n    if (this._parentFormField) {\n      this._preferredOverlayOrigin = this._parentFormField.getConnectedOverlayOrigin();\n    }\n    this._cleanupDetach?.();\n    this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n    this._applyModalPanelOwnership();\n    this._panelOpen = true;\n    this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n      this._changeDetectorRef.detectChanges();\n      this._positioningSettled();\n    });\n    this._overlayDir.attachOverlay();\n    this._keyManager.withHorizontalOrientation(null);\n    this._highlightCorrectOption();\n    this._changeDetectorRef.markForCheck();\n    // Required for the MDC form field to pick up when the overlay has been opened.\n    this.stateChanges.next();\n    // Simulate the animation event before we moved away from `@angular/animations`.\n    Promise.resolve().then(() => this.openedChange.emit(true));\n  }\n  /**\n   * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n   * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n   * panel. Track the modal we have changed so we can undo the changes on destroy.\n   */\n  _trackedModal = null;\n  /**\n   * If the autocomplete trigger is inside of an `aria-modal` element, connect\n   * that modal to the options panel with `aria-owns`.\n   *\n   * For some browser + screen reader combinations, when navigation is inside\n   * of an `aria-modal` element, the screen reader treats everything outside\n   * of that modal as hidden or invisible.\n   *\n   * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n   * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n   * from reaching the panel.\n   *\n   * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n   * the options panel. This effectively communicates to assistive technology that the\n   * options panel is part of the same interaction as the modal.\n   *\n   * At time of this writing, this issue is present in VoiceOver.\n   * See https://github.com/angular/components/issues/20694\n   */\n  _applyModalPanelOwnership() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modal = this._elementRef.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    if (!modal) {\n      // Most commonly, the autocomplete trigger is not inside a modal.\n      return;\n    }\n    const panelId = `${this.id}-panel`;\n    if (this._trackedModal) {\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    }\n    addAriaReferencedId(modal, 'aria-owns', panelId);\n    this._trackedModal = modal;\n  }\n  /** Clears the reference to the listbox overlay element from the modal it was added to. */\n  _clearFromModal() {\n    if (!this._trackedModal) {\n      // Most commonly, the autocomplete trigger is not used inside a modal.\n      return;\n    }\n    const panelId = `${this.id}-panel`;\n    removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    this._trackedModal = null;\n  }\n  /** Closes the overlay panel and focuses the host element. */\n  close() {\n    if (this._panelOpen) {\n      this._panelOpen = false;\n      this._exitAndDetach();\n      this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n      this._changeDetectorRef.markForCheck();\n      this._onTouched();\n      // Required for the MDC form field to pick up when the overlay has been closed.\n      this.stateChanges.next();\n      // Simulate the animation event before we moved away from `@angular/animations`.\n      Promise.resolve().then(() => this.openedChange.emit(false));\n    }\n  }\n  /** Triggers the exit animation and detaches the overlay at the end. */\n  _exitAndDetach() {\n    if (this._animationsDisabled || !this.panel) {\n      this._detachOverlay();\n      return;\n    }\n    this._cleanupDetach?.();\n    this._cleanupDetach = () => {\n      cleanupEvent();\n      clearTimeout(exitFallbackTimer);\n      this._cleanupDetach = undefined;\n    };\n    const panel = this.panel.nativeElement;\n    const cleanupEvent = this._renderer.listen(panel, 'animationend', event => {\n      if (event.animationName === '_mat-select-exit') {\n        this._cleanupDetach?.();\n        this._detachOverlay();\n      }\n    });\n    // Since closing the overlay depends on the animation, we have a fallback in case the panel\n    // doesn't animate. This can happen in some internal tests that do `* {animation: none}`.\n    const exitFallbackTimer = setTimeout(() => {\n      this._cleanupDetach?.();\n      this._detachOverlay();\n    }, 200);\n    panel.classList.add('mat-select-panel-exit');\n  }\n  /** Detaches the current overlay directive. */\n  _detachOverlay() {\n    this._overlayDir.detachOverlay();\n    // Some of the overlay detachment logic depends on change detection.\n    // Mark for check to ensure that things get picked up in a timely manner.\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Sets the select's value. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param value New value to be written to the model.\n   */\n  writeValue(value) {\n    this._assignValue(value);\n  }\n  /**\n   * Saves a callback function to be invoked when the select's value\n   * changes from user input. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the value changes.\n   */\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  /**\n   * Saves a callback function to be invoked when the select is blurred\n   * by the user. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the component has been touched.\n   */\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  /**\n   * Disables the select. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param isDisabled Sets whether the component is disabled.\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this._changeDetectorRef.markForCheck();\n    this.stateChanges.next();\n  }\n  /** Whether or not the overlay panel is open. */\n  get panelOpen() {\n    return this._panelOpen;\n  }\n  /** The currently selected option. */\n  get selected() {\n    return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n  }\n  /** The value displayed in the trigger. */\n  get triggerValue() {\n    if (this.empty) {\n      return '';\n    }\n    if (this._multiple) {\n      const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n      if (this._isRtl()) {\n        selectedOptions.reverse();\n      }\n      // TODO(crisbeto): delimiter should be configurable for proper localization.\n      return selectedOptions.join(', ');\n    }\n    return this._selectionModel.selected[0].viewValue;\n  }\n  /** Refreshes the error state of the select. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n  /** Whether the element is in RTL mode. */\n  _isRtl() {\n    return this._dir ? this._dir.value === 'rtl' : false;\n  }\n  /** Handles all keydown events on the select. */\n  _handleKeydown(event) {\n    if (!this.disabled) {\n      this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n    }\n  }\n  /** Handles keyboard events while the select is closed. */\n  _handleClosedKeydown(event) {\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW || keyCode === LEFT_ARROW || keyCode === RIGHT_ARROW;\n    const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n    const manager = this._keyManager;\n    // Open the select on ALT + arrow key to match the native <select>\n    if (!manager.isTyping() && isOpenKey && !hasModifierKey(event) || (this.multiple || event.altKey) && isArrowKey) {\n      event.preventDefault(); // prevents the page from scrolling down when pressing space\n      this.open();\n    } else if (!this.multiple) {\n      const previouslySelectedOption = this.selected;\n      manager.onKeydown(event);\n      const selectedOption = this.selected;\n      // Since the value has changed, we need to announce it ourselves.\n      if (selectedOption && previouslySelectedOption !== selectedOption) {\n        // We set a duration on the live announcement, because we want the live element to be\n        // cleared after a while so that users can't navigate to it using the arrow keys.\n        this._liveAnnouncer.announce(selectedOption.viewValue, 10000);\n      }\n    }\n  }\n  /** Handles keyboard events when the selected is open. */\n  _handleOpenKeydown(event) {\n    const manager = this._keyManager;\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n    const isTyping = manager.isTyping();\n    if (isArrowKey && event.altKey) {\n      // Close the select on ALT + arrow key to match the native <select>\n      event.preventDefault();\n      this.close();\n      // Don't do anything in this case if the user is typing,\n      // because the typing sequence can include the space key.\n    } else if (!isTyping && (keyCode === ENTER || keyCode === SPACE) && manager.activeItem && !hasModifierKey(event)) {\n      event.preventDefault();\n      manager.activeItem._selectViaInteraction();\n    } else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n      event.preventDefault();\n      const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n      this.options.forEach(option => {\n        if (!option.disabled) {\n          hasDeselectedOptions ? option.select() : option.deselect();\n        }\n      });\n    } else {\n      const previouslyFocusedIndex = manager.activeItemIndex;\n      manager.onKeydown(event);\n      if (this._multiple && isArrowKey && event.shiftKey && manager.activeItem && manager.activeItemIndex !== previouslyFocusedIndex) {\n        manager.activeItem._selectViaInteraction();\n      }\n    }\n  }\n  /** Handles keyboard events coming from the overlay. */\n  _handleOverlayKeydown(event) {\n    // TODO(crisbeto): prior to #30363 this was being handled inside the overlay directive, but we\n    // need control over the animation timing so we do it manually. We should remove the `keydown`\n    // listener from `.mat-mdc-select-panel` and handle all the events here. That may cause\n    // further test breakages so it's left for a follow-up.\n    if (event.keyCode === ESCAPE && !hasModifierKey(event)) {\n      event.preventDefault();\n      this.close();\n    }\n  }\n  _onFocus() {\n    if (!this.disabled) {\n      this._focused = true;\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n   * \"blur\" to the panel when it opens, causing a false positive.\n   */\n  _onBlur() {\n    this._focused = false;\n    this._keyManager?.cancelTypeahead();\n    if (!this.disabled && !this.panelOpen) {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n      this.stateChanges.next();\n    }\n  }\n  /** Returns the theme to be used on the panel. */\n  _getPanelTheme() {\n    return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n  }\n  /** Whether the select has a value. */\n  get empty() {\n    return !this._selectionModel || this._selectionModel.isEmpty();\n  }\n  _initializeSelection() {\n    // Defer setting the value in order to avoid the \"Expression\n    // has changed after it was checked\" errors from Angular.\n    Promise.resolve().then(() => {\n      if (this.ngControl) {\n        this._value = this.ngControl.value;\n      }\n      this._setSelectionByValue(this._value);\n      this.stateChanges.next();\n    });\n  }\n  /**\n   * Sets the selected option based on a value. If no option can be\n   * found with the designated value, the select trigger is cleared.\n   */\n  _setSelectionByValue(value) {\n    this.options.forEach(option => option.setInactiveStyles());\n    this._selectionModel.clear();\n    if (this.multiple && value) {\n      if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatSelectNonArrayValueError();\n      }\n      value.forEach(currentValue => this._selectOptionByValue(currentValue));\n      this._sortValues();\n    } else {\n      const correspondingOption = this._selectOptionByValue(value);\n      // Shift focus to the active item. Note that we shouldn't do this in multiple\n      // mode, because we don't know what option the user interacted with last.\n      if (correspondingOption) {\n        this._keyManager.updateActiveItem(correspondingOption);\n      } else if (!this.panelOpen) {\n        // Otherwise reset the highlighted option. Note that we only want to do this while\n        // closed, because doing it while open can shift the user's focus unnecessarily.\n        this._keyManager.updateActiveItem(-1);\n      }\n    }\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Finds and selects and option based on its value.\n   * @returns Option that has the corresponding value.\n   */\n  _selectOptionByValue(value) {\n    const correspondingOption = this.options.find(option => {\n      // Skip options that are already in the model. This allows us to handle cases\n      // where the same primitive value is selected multiple times.\n      if (this._selectionModel.isSelected(option)) {\n        return false;\n      }\n      try {\n        // Treat null as a special reset value.\n        return (option.value != null || this.canSelectNullableOptions) && this._compareWith(option.value, value);\n      } catch (error) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          // Notify developers of errors in their comparator.\n          console.warn(error);\n        }\n        return false;\n      }\n    });\n    if (correspondingOption) {\n      this._selectionModel.select(correspondingOption);\n    }\n    return correspondingOption;\n  }\n  /** Assigns a specific value to the select. Returns whether the value has changed. */\n  _assignValue(newValue) {\n    // Always re-assign an array, because it might have been mutated.\n    if (newValue !== this._value || this._multiple && Array.isArray(newValue)) {\n      if (this.options) {\n        this._setSelectionByValue(newValue);\n      }\n      this._value = newValue;\n      return true;\n    }\n    return false;\n  }\n  // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n  // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n  // recommendation.\n  //\n  // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n  // makes a few exceptions for compound widgets.\n  //\n  // From [Developing a Keyboard Interface](\n  // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n  //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n  //   Listbox...\"\n  //\n  // The user can focus disabled options using the keyboard, but the user cannot click disabled\n  // options.\n  _skipPredicate = option => {\n    if (this.panelOpen) {\n      // Support keyboard focusing disabled options in an ARIA listbox.\n      return false;\n    }\n    // When the panel is closed, skip over disabled options. Support options via the UP/DOWN arrow\n    // keys on a closed select. ARIA listbox interaction pattern is less relevant when the panel is\n    // closed.\n    return option.disabled;\n  };\n  /** Gets how wide the overlay panel should be. */\n  _getOverlayWidth(preferredOrigin) {\n    if (this.panelWidth === 'auto') {\n      const refToMeasure = preferredOrigin instanceof CdkOverlayOrigin ? preferredOrigin.elementRef : preferredOrigin || this._elementRef;\n      return refToMeasure.nativeElement.getBoundingClientRect().width;\n    }\n    return this.panelWidth === null ? '' : this.panelWidth;\n  }\n  /** Syncs the parent state with the individual options. */\n  _syncParentProperties() {\n    if (this.options) {\n      for (const option of this.options) {\n        option._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n  /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n  _initKeyManager() {\n    this._keyManager = new ActiveDescendantKeyManager(this.options).withTypeAhead(this.typeaheadDebounceInterval).withVerticalOrientation().withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr').withHomeAndEnd().withPageUpDown().withAllowedModifierKeys(['shiftKey']).skipPredicate(this._skipPredicate);\n    this._keyManager.tabOut.subscribe(() => {\n      if (this.panelOpen) {\n        // Select the active item when tabbing away. This is consistent with how the native\n        // select behaves. Note that we only want to do this in single selection mode.\n        if (!this.multiple && this._keyManager.activeItem) {\n          this._keyManager.activeItem._selectViaInteraction();\n        }\n        // Restore focus to the trigger before closing. Ensures that the focus\n        // position won't be lost if the user got focus into the overlay.\n        this.focus();\n        this.close();\n      }\n    });\n    this._keyManager.change.subscribe(() => {\n      if (this._panelOpen && this.panel) {\n        this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n      } else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n        this._keyManager.activeItem._selectViaInteraction();\n      }\n    });\n  }\n  /** Drops current option subscriptions and IDs and resets from scratch. */\n  _resetOptions() {\n    const changedOrDestroyed = merge(this.options.changes, this._destroy);\n    this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n      this._onSelect(event.source, event.isUserInput);\n      if (event.isUserInput && !this.multiple && this._panelOpen) {\n        this.close();\n        this.focus();\n      }\n    });\n    // Listen to changes in the internal state of the options and react accordingly.\n    // Handles cases like the labels of the selected options changing.\n    merge(...this.options.map(option => option._stateChanges)).pipe(takeUntil(changedOrDestroyed)).subscribe(() => {\n      // `_stateChanges` can fire as a result of a change in the label's DOM value which may\n      // be the result of an expression changing. We have to use `detectChanges` in order\n      // to avoid \"changed after checked\" errors (see #14793).\n      this._changeDetectorRef.detectChanges();\n      this.stateChanges.next();\n    });\n  }\n  /** Invoked when an option is clicked. */\n  _onSelect(option, isUserInput) {\n    const wasSelected = this._selectionModel.isSelected(option);\n    if (!this.canSelectNullableOptions && option.value == null && !this._multiple) {\n      option.deselect();\n      this._selectionModel.clear();\n      if (this.value != null) {\n        this._propagateChanges(option.value);\n      }\n    } else {\n      if (wasSelected !== option.selected) {\n        option.selected ? this._selectionModel.select(option) : this._selectionModel.deselect(option);\n      }\n      if (isUserInput) {\n        this._keyManager.setActiveItem(option);\n      }\n      if (this.multiple) {\n        this._sortValues();\n        if (isUserInput) {\n          // In case the user selected the option with their mouse, we\n          // want to restore focus back to the trigger, in order to\n          // prevent the select keyboard controls from clashing with\n          // the ones from `mat-option`.\n          this.focus();\n        }\n      }\n    }\n    if (wasSelected !== this._selectionModel.isSelected(option)) {\n      this._propagateChanges();\n    }\n    this.stateChanges.next();\n  }\n  /** Sorts the selected values in the selected based on their order in the panel. */\n  _sortValues() {\n    if (this.multiple) {\n      const options = this.options.toArray();\n      this._selectionModel.sort((a, b) => {\n        return this.sortComparator ? this.sortComparator(a, b, options) : options.indexOf(a) - options.indexOf(b);\n      });\n      this.stateChanges.next();\n    }\n  }\n  /** Emits change event to set the model value. */\n  _propagateChanges(fallbackValue) {\n    let valueToEmit;\n    if (this.multiple) {\n      valueToEmit = this.selected.map(option => option.value);\n    } else {\n      valueToEmit = this.selected ? this.selected.value : fallbackValue;\n    }\n    this._value = valueToEmit;\n    this.valueChange.emit(valueToEmit);\n    this._onChange(valueToEmit);\n    this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Highlights the selected item. If no option is selected, it will highlight\n   * the first *enabled* option.\n   */\n  _highlightCorrectOption() {\n    if (this._keyManager) {\n      if (this.empty) {\n        // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n        // because it activates the first option that passes the skip predicate, rather than the\n        // first *enabled* option.\n        let firstEnabledOptionIndex = -1;\n        for (let index = 0; index < this.options.length; index++) {\n          const option = this.options.get(index);\n          if (!option.disabled) {\n            firstEnabledOptionIndex = index;\n            break;\n          }\n        }\n        this._keyManager.setActiveItem(firstEnabledOptionIndex);\n      } else {\n        this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n      }\n    }\n  }\n  /** Whether the panel is allowed to open. */\n  _canOpen() {\n    return !this._panelOpen && !this.disabled && this.options?.length > 0 && !!this._overlayDir;\n  }\n  /** Focuses the select element. */\n  focus(options) {\n    this._elementRef.nativeElement.focus(options);\n  }\n  /** Gets the aria-labelledby for the select panel. */\n  _getPanelAriaLabelledby() {\n    if (this.ariaLabel) {\n      return null;\n    }\n    const labelId = this._parentFormField?.getLabelId() || null;\n    const labelExpression = labelId ? labelId + ' ' : '';\n    return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n  }\n  /** Determines the `aria-activedescendant` to be set on the host. */\n  _getAriaActiveDescendant() {\n    if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n      return this._keyManager.activeItem.id;\n    }\n    return null;\n  }\n  /** Gets the aria-labelledby of the select component trigger. */\n  _getTriggerAriaLabelledby() {\n    if (this.ariaLabel) {\n      return null;\n    }\n    let value = this._parentFormField?.getLabelId() || '';\n    if (this.ariaLabelledby) {\n      value += ' ' + this.ariaLabelledby;\n    }\n    // The value should not be used for the trigger's aria-labelledby,\n    // but this currently \"breaks\" accessibility tests since they complain\n    // there is no aria-labelledby. This is because they are not setting an\n    // appropriate label on the form field or select.\n    // TODO: remove this conditional after fixing clients by ensuring their\n    // selects have a label applied.\n    if (!value) {\n      value = this._valueId;\n    }\n    return value;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get describedByIds() {\n    const element = this._elementRef.nativeElement;\n    const existingDescribedBy = element.getAttribute('aria-describedby');\n    return existingDescribedBy?.split(' ') || [];\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids) {\n    if (ids.length) {\n      this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      this._elementRef.nativeElement.removeAttribute('aria-describedby');\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick() {\n    this.focus();\n    this.open();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat() {\n    // Since the panel doesn't overlap the trigger, we\n    // want the label to only float when there's a value.\n    return this.panelOpen || !this.empty || this.focused && !!this.placeholder;\n  }\n  static ɵfac = function MatSelect_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSelect)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSelect,\n    selectors: [[\"mat-select\"]],\n    contentQueries: function MatSelect_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MAT_SELECT_TRIGGER, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatOption, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_OPTGROUP, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.customTrigger = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionGroups = _t);\n      }\n    },\n    viewQuery: function MatSelect_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(CdkConnectedOverlay, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trigger = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._overlayDir = _t.first);\n      }\n    },\n    hostAttrs: [\"role\", \"combobox\", \"aria-haspopup\", \"listbox\", 1, \"mat-mdc-select\"],\n    hostVars: 19,\n    hostBindings: function MatSelect_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown\", function MatSelect_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        })(\"focus\", function MatSelect_focus_HostBindingHandler() {\n          return ctx._onFocus();\n        })(\"blur\", function MatSelect_blur_HostBindingHandler() {\n          return ctx._onBlur();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"id\", ctx.id)(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-controls\", ctx.panelOpen ? ctx.id + \"-panel\" : null)(\"aria-expanded\", ctx.panelOpen)(\"aria-label\", ctx.ariaLabel || null)(\"aria-required\", ctx.required.toString())(\"aria-disabled\", ctx.disabled.toString())(\"aria-invalid\", ctx.errorState)(\"aria-activedescendant\", ctx._getAriaActiveDescendant());\n        i0.ɵɵclassProp(\"mat-mdc-select-disabled\", ctx.disabled)(\"mat-mdc-select-invalid\", ctx.errorState)(\"mat-mdc-select-required\", ctx.required)(\"mat-mdc-select-empty\", ctx.empty)(\"mat-mdc-select-multiple\", ctx.multiple);\n      }\n    },\n    inputs: {\n      userAriaDescribedBy: [0, \"aria-describedby\", \"userAriaDescribedBy\"],\n      panelClass: \"panelClass\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n      hideSingleSelectionIndicator: [2, \"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute],\n      placeholder: \"placeholder\",\n      required: [2, \"required\", \"required\", booleanAttribute],\n      multiple: [2, \"multiple\", \"multiple\", booleanAttribute],\n      disableOptionCentering: [2, \"disableOptionCentering\", \"disableOptionCentering\", booleanAttribute],\n      compareWith: \"compareWith\",\n      value: \"value\",\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n      errorStateMatcher: \"errorStateMatcher\",\n      typeaheadDebounceInterval: [2, \"typeaheadDebounceInterval\", \"typeaheadDebounceInterval\", numberAttribute],\n      sortComparator: \"sortComparator\",\n      id: \"id\",\n      panelWidth: \"panelWidth\",\n      canSelectNullableOptions: [2, \"canSelectNullableOptions\", \"canSelectNullableOptions\", booleanAttribute]\n    },\n    outputs: {\n      openedChange: \"openedChange\",\n      _openedStream: \"opened\",\n      _closedStream: \"closed\",\n      selectionChange: \"selectionChange\",\n      valueChange: \"valueChange\"\n    },\n    exportAs: [\"matSelect\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatFormFieldControl,\n      useExisting: MatSelect\n    }, {\n      provide: MAT_OPTION_PARENT_COMPONENT,\n      useExisting: MatSelect\n    }]), i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c3,\n    decls: 11,\n    vars: 9,\n    consts: [[\"fallbackOverlayOrigin\", \"cdkOverlayOrigin\", \"trigger\", \"\"], [\"panel\", \"\"], [\"cdk-overlay-origin\", \"\", 1, \"mat-mdc-select-trigger\", 3, \"click\"], [1, \"mat-mdc-select-value\"], [1, \"mat-mdc-select-placeholder\", \"mat-mdc-select-min-line\"], [1, \"mat-mdc-select-value-text\"], [1, \"mat-mdc-select-arrow-wrapper\"], [1, \"mat-mdc-select-arrow\"], [\"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"height\", \"24px\", \"focusable\", \"false\", \"aria-hidden\", \"true\"], [\"d\", \"M7 10l5 5 5-5z\"], [\"cdk-connected-overlay\", \"\", \"cdkConnectedOverlayLockPosition\", \"\", \"cdkConnectedOverlayHasBackdrop\", \"\", \"cdkConnectedOverlayBackdropClass\", \"cdk-overlay-transparent-backdrop\", 3, \"detach\", \"backdropClick\", \"overlayKeydown\", \"cdkConnectedOverlayDisableClose\", \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayFlexibleDimensions\"], [1, \"mat-mdc-select-min-line\"], [\"role\", \"listbox\", \"tabindex\", \"-1\", 3, \"keydown\", \"ngClass\"]],\n    template: function MatSelect_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef(_c2);\n        i0.ɵɵelementStart(0, \"div\", 2, 0);\n        i0.ɵɵlistener(\"click\", function MatSelect_Template_div_click_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.open());\n        });\n        i0.ɵɵelementStart(3, \"div\", 3);\n        i0.ɵɵconditionalCreate(4, MatSelect_Conditional_4_Template, 2, 1, \"span\", 4)(5, MatSelect_Conditional_5_Template, 3, 1, \"span\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(8, \"svg\", 8);\n        i0.ɵɵelement(9, \"path\", 9);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(10, MatSelect_ng_template_10_Template, 3, 10, \"ng-template\", 10);\n        i0.ɵɵlistener(\"detach\", function MatSelect_Template_ng_template_detach_10_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.close());\n        })(\"backdropClick\", function MatSelect_Template_ng_template_backdropClick_10_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.close());\n        })(\"overlayKeydown\", function MatSelect_Template_ng_template_overlayKeydown_10_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handleOverlayKeydown($event));\n        });\n      }\n      if (rf & 2) {\n        const fallbackOverlayOrigin_r4 = i0.ɵɵreference(1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵattribute(\"id\", ctx._valueId);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.empty ? 4 : 5);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"cdkConnectedOverlayDisableClose\", true)(\"cdkConnectedOverlayPanelClass\", ctx._overlayPanelClass)(\"cdkConnectedOverlayScrollStrategy\", ctx._scrollStrategy)(\"cdkConnectedOverlayOrigin\", ctx._preferredOverlayOrigin || fallbackOverlayOrigin_r4)(\"cdkConnectedOverlayPositions\", ctx._positions)(\"cdkConnectedOverlayWidth\", ctx._overlayWidth)(\"cdkConnectedOverlayFlexibleDimensions\", true);\n      }\n    },\n    dependencies: [CdkOverlayOrigin, CdkConnectedOverlay, NgClass],\n    styles: [\"@keyframes _mat-select-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-select-exit{from{opacity:1}to{opacity:0}}.mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color, var(--mat-sys-on-surface));font-family:var(--mat-select-trigger-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-select-trigger-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-select-trigger-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-select-trigger-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-select-trigger-text-tracking, var(--mat-sys-body-large-tracking))}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-disabled .mat-mdc-select-placeholder{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color, var(--mat-sys-error))}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}@media(forced-colors: active){.mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .mat-mdc-select-arrow svg{fill:GrayText}}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:relative;background-color:var(--mat-select-panel-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-select-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-select-panel-animations-enabled{animation:_mat-select-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-select-panel-animations-enabled.mat-select-panel-exit{animation:_mat-select-exit 100ms linear}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field:not(.mat-form-field-animations-enabled) .mat-mdc-select-placeholder,._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform, translateY(-8px))}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelect, [{\n    type: Component,\n    args: [{\n      selector: 'mat-select',\n      exportAs: 'matSelect',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'role': 'combobox',\n        'aria-haspopup': 'listbox',\n        'class': 'mat-mdc-select',\n        '[attr.id]': 'id',\n        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n        '[attr.aria-controls]': 'panelOpen ? id + \"-panel\" : null',\n        '[attr.aria-expanded]': 'panelOpen',\n        '[attr.aria-label]': 'ariaLabel || null',\n        '[attr.aria-required]': 'required.toString()',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.aria-invalid]': 'errorState',\n        '[attr.aria-activedescendant]': '_getAriaActiveDescendant()',\n        '[class.mat-mdc-select-disabled]': 'disabled',\n        '[class.mat-mdc-select-invalid]': 'errorState',\n        '[class.mat-mdc-select-required]': 'required',\n        '[class.mat-mdc-select-empty]': 'empty',\n        '[class.mat-mdc-select-multiple]': 'multiple',\n        '(keydown)': '_handleKeydown($event)',\n        '(focus)': '_onFocus()',\n        '(blur)': '_onBlur()'\n      },\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatSelect\n      }, {\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatSelect\n      }],\n      imports: [CdkOverlayOrigin, CdkConnectedOverlay, NgClass],\n      template: \"<div cdk-overlay-origin\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"open()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n\\n  <div class=\\\"mat-mdc-select-value\\\" [attr.id]=\\\"_valueId\\\">\\n    @if (empty) {\\n      <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\">{{placeholder}}</span>\\n    } @else {\\n      <span class=\\\"mat-mdc-select-value-text\\\">\\n        @if (customTrigger) {\\n          <ng-content select=\\\"mat-select-trigger\\\"></ng-content>\\n        } @else {\\n          <span class=\\\"mat-mdc-select-min-line\\\">{{triggerValue}}</span>\\n        }\\n      </span>\\n    }\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayDisableClose]=\\\"true\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  [cdkConnectedOverlayFlexibleDimensions]=\\\"true\\\"\\n  (detach)=\\\"close()\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (overlayKeydown)=\\\"_handleOverlayKeydown($event)\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [class.mat-select-panel-animations-enabled]=\\\"!_animationsDisabled\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\"@keyframes _mat-select-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-select-exit{from{opacity:1}to{opacity:0}}.mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color, var(--mat-sys-on-surface));font-family:var(--mat-select-trigger-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-select-trigger-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-select-trigger-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-select-trigger-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-select-trigger-text-tracking, var(--mat-sys-body-large-tracking))}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-disabled .mat-mdc-select-placeholder{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color, var(--mat-sys-error))}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}@media(forced-colors: active){.mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .mat-mdc-select-arrow svg{fill:GrayText}}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:relative;background-color:var(--mat-select-panel-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-select-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-select-panel-animations-enabled{animation:_mat-select-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-select-panel-animations-enabled.mat-select-panel-exit{animation:_mat-select-exit 100ms linear}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field:not(.mat-form-field-animations-enabled) .mat-mdc-select-placeholder,._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform, translateY(-8px))}\\n\"]\n    }]\n  }], () => [], {\n    options: [{\n      type: ContentChildren,\n      args: [MatOption, {\n        descendants: true\n      }]\n    }],\n    optionGroups: [{\n      type: ContentChildren,\n      args: [MAT_OPTGROUP, {\n        descendants: true\n      }]\n    }],\n    customTrigger: [{\n      type: ContentChild,\n      args: [MAT_SELECT_TRIGGER]\n    }],\n    userAriaDescribedBy: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    trigger: [{\n      type: ViewChild,\n      args: ['trigger']\n    }],\n    panel: [{\n      type: ViewChild,\n      args: ['panel']\n    }],\n    _overlayDir: [{\n      type: ViewChild,\n      args: [CdkConnectedOverlay]\n    }],\n    panelClass: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }],\n    hideSingleSelectionIndicator: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    multiple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableOptionCentering: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    errorStateMatcher: [{\n      type: Input\n    }],\n    typeaheadDebounceInterval: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    sortComparator: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    panelWidth: [{\n      type: Input\n    }],\n    canSelectNullableOptions: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    openedChange: [{\n      type: Output\n    }],\n    _openedStream: [{\n      type: Output,\n      args: ['opened']\n    }],\n    _closedStream: [{\n      type: Output,\n      args: ['closed']\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    valueChange: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\nclass MatSelectTrigger {\n  static ɵfac = function MatSelectTrigger_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSelectTrigger)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSelectTrigger,\n    selectors: [[\"mat-select-trigger\"]],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_SELECT_TRIGGER,\n      useExisting: MatSelectTrigger\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectTrigger, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-select-trigger',\n      providers: [{\n        provide: MAT_SELECT_TRIGGER,\n        useExisting: MatSelectTrigger\n      }]\n    }]\n  }], null, null);\n})();\nclass MatSelectModule {\n  static ɵfac = function MatSelectModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSelectModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatSelectModule,\n    imports: [OverlayModule, MatOptionModule, MatCommonModule, MatSelect, MatSelectTrigger],\n    exports: [CdkScrollableModule, MatFormFieldModule, MatSelect, MatSelectTrigger, MatOptionModule, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n    imports: [OverlayModule, MatOptionModule, MatCommonModule, CdkScrollableModule, MatFormFieldModule, MatOptionModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, MatOptionModule, MatCommonModule, MatSelect, MatSelectTrigger],\n      exports: [CdkScrollableModule, MatFormFieldModule, MatSelect, MatSelectTrigger, MatOptionModule, MatCommonModule],\n      providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\nexport { MAT_SELECT_CONFIG, MAT_SELECT_SCROLL_STRATEGY, MAT_SELECT_SCROLL_STRATEGY_PROVIDER, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, MAT_SELECT_TRIGGER, MatSelect, MatSelectChange, MatSelectModule, MatSelectTrigger };\n", "export { <PERSON><PERSON>pt<PERSON>, MatOption } from './option.mjs';\nexport { <PERSON><PERSON><PERSON><PERSON>, MatFormField, <PERSON>Hint, <PERSON>Label, MatPrefix, MatSuffix } from './form-field2.mjs';\nexport { MAT_SELECT_CONFIG, MAT_SELECT_SCROLL_STRATEGY, MAT_SELECT_SCROLL_STRATEGY_PROVIDER, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, MAT_SELECT_TRIGGER, MatSelect, MatSelectChange, MatSelectModule, MatSelectTrigger } from './select-module.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/keycodes';\nimport '@angular/core';\nimport 'rxjs';\nimport './ripple.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/private';\nimport './animation.mjs';\nimport '@angular/cdk/layout';\nimport './pseudo-checkbox.mjs';\nimport './structural-styles.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport '@angular/cdk/overlay';\nimport '@angular/cdk/scrolling';\nimport '@angular/cdk/collections';\nimport '@angular/forms';\nimport './error-options.mjs';\nimport './error-state.mjs';\nimport './option-module.mjs';\nimport './ripple-module.mjs';\nimport './common-module.mjs';\nimport './pseudo-checkbox-module.mjs';\nimport './form-field-module.mjs';\nimport '@angular/cdk/observers';\n\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matSelectAnimations = {\n    // Represents\n    // trigger('transformPanel', [\n    //   state(\n    //     'void',\n    //     style({\n    //       opacity: 0,\n    //       transform: 'scale(1, 0.8)',\n    //     }),\n    //   ),\n    //   transition(\n    //     'void => showing',\n    //     animate(\n    //       '120ms cubic-bezier(0, 0, 0.2, 1)',\n    //       style({\n    //         opacity: 1,\n    //         transform: 'scale(1, 1)',\n    //       }),\n    //     ),\n    //   ),\n    //   transition('* => void', animate('100ms linear', style({opacity: 0}))),\n    // ])\n    /** This animation transforms the select's overlay panel on and off the page. */\n    transformPanel: {\n        type: 7,\n        name: 'transformPanel',\n        definitions: [\n            {\n                type: 0,\n                name: 'void',\n                styles: {\n                    type: 6,\n                    styles: { opacity: 0, transform: 'scale(1, 0.8)' },\n                    offset: null,\n                },\n            },\n            {\n                type: 1,\n                expr: 'void => showing',\n                animation: {\n                    type: 4,\n                    styles: {\n                        type: 6,\n                        styles: { opacity: 1, transform: 'scale(1, 1)' },\n                        offset: null,\n                    },\n                    timings: '120ms cubic-bezier(0, 0, 0.2, 1)',\n                },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* => void',\n                animation: {\n                    type: 4,\n                    styles: { type: 6, styles: { opacity: 0 }, offset: null },\n                    timings: '100ms linear',\n                },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { matSelectAnimations };\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,aAAa,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMd,OAAO,IAAI,MAAM;AACf,aAAS,YAAY,KAAK,YAAY;AACpC,eAAS,IAAI,IAAI;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,UAAU;AACf,SAAK,WAAW,KAAK,QAAQ;AAC7B,WAAO,MAAM;AACX,WAAK,aAAa,KAAK,WAAW,OAAO,gBAAc;AACrD,eAAO,aAAa;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,CAAC;AAAA,EACrB;AAAA,EACA,OAAO,OAAO,SAAS,kCAAkC,mBAAmB;AAC1E,WAAO,KAAK,qBAAqB,4BAA2B;AAAA,EAC9D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,2BAA0B;AAAA,IACnC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AClDH,IAAM,iBAAN,MAAqB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,aAAa,oBAAI,IAAI;AAAA;AAAA,EAErB,oBAAoB,CAAC;AAAA;AAAA,EAErB,kBAAkB,CAAC;AAAA;AAAA,EAEnB;AAAA;AAAA,EAEA,IAAI,WAAW;AACX,QAAI,CAAC,KAAK,WAAW;AACjB,WAAK,YAAY,MAAM,KAAK,KAAK,WAAW,OAAO,CAAC;AAAA,IACxD;AACA,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA,EAEA,UAAU,IAAI,QAAQ;AAAA,EACtB,YAAY,YAAY,OAAO,yBAAyB,eAAe,MAAM,aAAa;AACtF,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,QAAI,2BAA2B,wBAAwB,QAAQ;AAC3D,UAAI,WAAW;AACX,gCAAwB,QAAQ,WAAS,KAAK,cAAc,KAAK,CAAC;AAAA,MACtE,OACK;AACD,aAAK,cAAc,wBAAwB,CAAC,CAAC;AAAA,MACjD;AAEA,WAAK,gBAAgB,SAAS;AAAA,IAClC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,QAAQ;AACd,SAAK,uBAAuB,MAAM;AAClC,WAAO,QAAQ,WAAS,KAAK,cAAc,KAAK,CAAC;AACjD,UAAM,UAAU,KAAK,kBAAkB;AACvC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,QAAQ;AAChB,SAAK,uBAAuB,MAAM;AAClC,WAAO,QAAQ,WAAS,KAAK,gBAAgB,KAAK,CAAC;AACnD,UAAM,UAAU,KAAK,kBAAkB;AACvC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,QAAQ;AACpB,SAAK,uBAAuB,MAAM;AAClC,UAAM,YAAY,KAAK;AACvB,UAAM,iBAAiB,IAAI,IAAI,OAAO,IAAI,WAAS,KAAK,kBAAkB,KAAK,CAAC,CAAC;AACjF,WAAO,QAAQ,WAAS,KAAK,cAAc,KAAK,CAAC;AACjD,cACK,OAAO,WAAS,CAAC,eAAe,IAAI,KAAK,kBAAkB,OAAO,cAAc,CAAC,CAAC,EAClF,QAAQ,WAAS,KAAK,gBAAgB,KAAK,CAAC;AACjD,UAAM,UAAU,KAAK,kBAAkB;AACvC,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAO;AACV,WAAO,KAAK,WAAW,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,KAAK,OAAO,KAAK;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,aAAa,MAAM;AACrB,SAAK,WAAW;AAChB,UAAM,UAAU,KAAK,kBAAkB;AACvC,QAAI,YAAY;AACZ,WAAK,iBAAiB;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,OAAO;AACd,WAAO,KAAK,WAAW,IAAI,KAAK,kBAAkB,KAAK,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACN,WAAO,KAAK,WAAW,SAAS;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,WAAO,CAAC,KAAK,QAAQ;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK,WAAW;AACZ,QAAI,KAAK,aAAa,KAAK,UAAU;AACjC,WAAK,UAAU,KAAK,SAAS;AAAA,IACjC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB;AAClB,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA,EAEA,mBAAmB;AAEf,SAAK,YAAY;AACjB,QAAI,KAAK,gBAAgB,UAAU,KAAK,kBAAkB,QAAQ;AAC9D,WAAK,QAAQ,KAAK;AAAA,QACd,QAAQ;AAAA,QACR,OAAO,KAAK;AAAA,QACZ,SAAS,KAAK;AAAA,MAClB,CAAC;AACD,WAAK,oBAAoB,CAAC;AAC1B,WAAK,kBAAkB,CAAC;AAAA,IAC5B;AAAA,EACJ;AAAA;AAAA,EAEA,cAAc,OAAO;AACjB,YAAQ,KAAK,kBAAkB,KAAK;AACpC,QAAI,CAAC,KAAK,WAAW,KAAK,GAAG;AACzB,UAAI,CAAC,KAAK,WAAW;AACjB,aAAK,WAAW;AAAA,MACpB;AACA,UAAI,CAAC,KAAK,WAAW,KAAK,GAAG;AACzB,aAAK,WAAW,IAAI,KAAK;AAAA,MAC7B;AACA,UAAI,KAAK,cAAc;AACnB,aAAK,gBAAgB,KAAK,KAAK;AAAA,MACnC;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACnB,YAAQ,KAAK,kBAAkB,KAAK;AACpC,QAAI,KAAK,WAAW,KAAK,GAAG;AACxB,WAAK,WAAW,OAAO,KAAK;AAC5B,UAAI,KAAK,cAAc;AACnB,aAAK,kBAAkB,KAAK,KAAK;AAAA,MACrC;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA,EAEA,aAAa;AACT,QAAI,CAAC,KAAK,QAAQ,GAAG;AACjB,WAAK,WAAW,QAAQ,WAAS,KAAK,gBAAgB,KAAK,CAAC;AAAA,IAChE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB,QAAQ;AAC3B,QAAI,OAAO,SAAS,KAAK,CAAC,KAAK,cAAc,OAAO,cAAc,eAAe,YAAY;AACzF,YAAM,wCAAwC;AAAA,IAClD;AAAA,EACJ;AAAA;AAAA,EAEA,oBAAoB;AAChB,WAAO,CAAC,EAAE,KAAK,kBAAkB,UAAU,KAAK,gBAAgB;AAAA,EACpE;AAAA;AAAA,EAEA,kBAAkB,YAAY,WAAW;AACrC,QAAI,CAAC,KAAK,aAAa;AACnB,aAAO;AAAA,IACX,OACK;AACD,kBAAY,aAAa,KAAK;AAC9B,eAAS,iBAAiB,WAAW;AACjC,YAAI,KAAK,YAAY,YAAY,aAAa,GAAG;AAC7C,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAMA,SAAS,0CAA0C;AAC/C,SAAO,MAAM,yEAAyE;AAC1F;;;AC7LA,IAAM,MAAM,CAAC,SAAS;AACtB,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,GAAG,GAAG;AAC1C,IAAM,MAAM,CAAC,sBAAsB,GAAG;AACtC,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,WAAW;AAAA,EACzC;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,YAAY;AAAA,EAC1C;AACF;AACA,SAAS,iCAAiC,IAAI,KAAK;AACjD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,oBAAoB,GAAG,gDAAgD,GAAG,CAAC,EAAE,GAAG,gDAAgD,GAAG,GAAG,QAAQ,EAAE;AACnJ,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,OAAO,gBAAgB,IAAI,CAAC;AAAA,EAC/C;AACF;AACA,SAAS,kCAAkC,IAAI,KAAK;AAClD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,IAAI,CAAC;AACjC,IAAG,WAAW,WAAW,SAAS,yDAAyD,QAAQ;AACjG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,MAAM,CAAC;AAAA,IACrD,CAAC;AACD,IAAG,aAAa,GAAG,CAAC;AACpB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAc,eAAe,iEAAiE,OAAO,eAAe,CAAC,CAAC;AACzH,IAAG,YAAY,uCAAuC,CAAC,OAAO,mBAAmB;AACjF,IAAG,WAAW,WAAW,OAAO,UAAU;AAC1C,IAAG,YAAY,MAAM,OAAO,KAAK,QAAQ,EAAE,wBAAwB,OAAO,QAAQ,EAAE,cAAc,OAAO,aAAa,IAAI,EAAE,mBAAmB,OAAO,wBAAwB,CAAC;AAAA,EACjL;AACF;AACA,SAAS,mCAAmC;AAC1C,SAAO,MAAM,+DAA+D;AAC9E;AAOA,SAAS,iCAAiC;AACxC,SAAO,MAAM,oDAAoD;AACnE;AAMA,SAAS,oCAAoC;AAC3C,SAAO,MAAM,mCAAmC;AAClD;AAGA,IAAM,6BAA6B,IAAI,eAAe,8BAA8B;AAAA,EAClF,YAAY;AAAA,EACZ,SAAS,MAAM;AACb,UAAM,WAAW,OAAO,QAAQ;AAChC,WAAO,MAAM,+BAA+B,QAAQ;AAAA,EACtD;AACF,CAAC;AAMD,SAAS,4CAA4C,UAAU;AAC7D,QAAM,WAAW,OAAO,QAAQ;AAChC,SAAO,MAAM,+BAA+B,QAAQ;AACtD;AAEA,IAAM,oBAAoB,IAAI,eAAe,mBAAmB;AAMhE,IAAM,sCAAsC;AAAA,EAC1C,SAAS;AAAA,EACT,MAAM,CAAC;AAAA,EACP,YAAY;AACd;AAMA,IAAM,qBAAqB,IAAI,eAAe,kBAAkB;AAEhE,IAAM,kBAAN,MAAsB;AAAA,EACpB;AAAA,EACA;AAAA,EACA,YACA,QACA,OAAO;AACL,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACf;AACF;AACA,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,iBAAiB,OAAO,aAAa;AAAA,EACrC,qBAAqB,OAAO,iBAAiB;AAAA,EAC7C,cAAc,OAAO,UAAU;AAAA,EAC/B,OAAO,OAAO,gBAAgB;AAAA,IAC5B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,eAAe,OAAO,YAAY;AAAA,EAClC,YAAY,OAAO,SAAS;AAAA,EAC5B,mBAAmB,OAAO,gBAAgB;AAAA,IACxC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,OAAO,WAAW;AAAA,IAC5B,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,iBAAiB,OAAO,aAAa;AAAA,EACrC,kBAAkB,OAAO,mBAAmB;AAAA,IAC1C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,sBAAsB,oBAAoB;AAAA,EAC1C,eAAe,IAAI,QAAQ;AAAA,EAC3B;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,CAAC;AAAA,IACZ,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,YAAY;AAAA,EACd,CAAC;AAAA;AAAA,EAED,sBAAsB,OAAO;AAC3B,UAAM,SAAS,KAAK,QAAQ,QAAQ,EAAE,KAAK;AAC3C,QAAI,QAAQ;AACV,YAAM,QAAQ,KAAK,MAAM;AACzB,YAAM,aAAa,8BAA8B,OAAO,KAAK,SAAS,KAAK,YAAY;AACvF,YAAM,UAAU,OAAO,gBAAgB;AACvC,UAAI,UAAU,KAAK,eAAe,GAAG;AAInC,cAAM,YAAY;AAAA,MACpB,OAAO;AACL,cAAM,YAAY,yBAAyB,QAAQ,WAAW,QAAQ,cAAc,MAAM,WAAW,MAAM,YAAY;AAAA,MACzH;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,sBAAsB;AACpB,SAAK,sBAAsB,KAAK,YAAY,mBAAmB,CAAC;AAAA,EAClE;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACrB,WAAO,IAAI,gBAAgB,MAAM,KAAK;AAAA,EACxC;AAAA;AAAA,EAEA,yBAAyB,OAAO,0BAA0B;AAAA;AAAA,EAE1D,aAAa;AAAA;AAAA,EAEb,eAAe,CAAC,IAAI,OAAO,OAAO;AAAA;AAAA,EAElC,OAAO,KAAK,aAAa,MAAM,aAAa;AAAA;AAAA,EAE5C,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB;AAAA;AAAA,EAEA,WAAW,IAAI,QAAQ;AAAA;AAAA,EAEvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,YAAY,MAAM;AAAA,EAAC;AAAA;AAAA,EAEnB,aAAa,MAAM;AAAA,EAAC;AAAA;AAAA,EAEpB,WAAW,KAAK,aAAa,MAAM,mBAAmB;AAAA;AAAA,EAEtD;AAAA,EACA,qBAAqB,KAAK,iBAAiB,qBAAqB;AAAA;AAAA,EAEhE,IAAI,UAAU;AACZ,WAAO,KAAK,YAAY,KAAK;AAAA,EAC/B;AAAA,EACA,WAAW;AAAA;AAAA,EAEX,cAAc;AAAA;AAAA,EAEd;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,WAAW;AAAA;AAAA,EAEX,IAAI,gBAAgB;AAClB,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,eAAe,IAAI,KAAK;AAAA,EAC/B;AAAA,EACA,iBAAiB,OAAO,OAAO,GAAI,YAAY,CAAC;AAAA,IAC9C,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA;AAAA,EAER,WAAW;AAAA;AAAA,EAEX,IAAI,+BAA+B;AACjC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,6BAA6B,OAAO;AACtC,SAAK,gCAAgC;AACrC,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,gCAAgC,KAAK,iBAAiB,gCAAgC;AAAA;AAAA,EAEtF,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,eAAe;AACpB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,KAAK,WAAW,SAAS,aAAa,WAAW,QAAQ,KAAK;AAAA,EACzF;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,KAAK,oBAAoB,OAAO,cAAc,eAAe,YAAY;AAC3E,YAAM,iCAAiC;AAAA,IACzC;AACA,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,yBAAyB,KAAK,iBAAiB,0BAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzE,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,IAAI;AAClB,QAAI,OAAO,OAAO,eAAe,OAAO,cAAc,eAAe,YAAY;AAC/E,YAAM,kCAAkC;AAAA,IAC1C;AACA,SAAK,eAAe;AACpB,QAAI,KAAK,iBAAiB;AAExB,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,UAAU;AAClB,UAAM,cAAc,KAAK,aAAa,QAAQ;AAC9C,QAAI,aAAa;AACf,WAAK,UAAU,QAAQ;AAAA,IACzB;AAAA,EACF;AAAA,EACA;AAAA;AAAA,EAEA,YAAY;AAAA;AAAA,EAEZ;AAAA;AAAA,EAEA,IAAI,oBAAoB;AACtB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,kBAAkB,OAAO;AAC3B,SAAK,mBAAmB,UAAU;AAAA,EACpC;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA,EAEA,IAAI,KAAK;AACP,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,GAAG,OAAO;AACZ,SAAK,MAAM,SAAS,KAAK;AACzB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,KAAK,mBAAmB,OAAO,KAAK,gBAAgB,eAAe,cAAc,KAAK,gBAAgB,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOhI,2BAA2B,KAAK,iBAAiB,4BAA4B;AAAA;AAAA,EAE7E,yBAAyB,MAAM,MAAM;AACnC,UAAM,UAAU,KAAK;AACrB,QAAI,SAAS;AACX,aAAO,QAAQ,QAAQ,KAAK,UAAU,OAAO,GAAG,UAAU,MAAM,MAAM,GAAG,QAAQ,IAAI,YAAU,OAAO,iBAAiB,CAAC,CAAC,CAAC;AAAA,IAC5H;AACA,WAAO,KAAK,aAAa,KAAK,UAAU,MAAM,KAAK,sBAAsB,CAAC;AAAA,EAC5E,CAAC;AAAA;AAAA,EAED,eAAe,IAAI,aAAa;AAAA;AAAA,EAEhC,gBAAgB,KAAK,aAAa,KAAK,OAAO,OAAK,CAAC,GAAG,IAAI,MAAM;AAAA,EAAC,CAAC,CAAC;AAAA;AAAA,EAEpE,gBAAgB,KAAK,aAAa,KAAK,OAAO,OAAK,CAAC,CAAC,GAAG,IAAI,MAAM;AAAA,EAAC,CAAC,CAAC;AAAA;AAAA,EAErE,kBAAkB,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnC,cAAc,IAAI,aAAa;AAAA,EAC/B,cAAc;AACZ,UAAM,2BAA2B,OAAO,iBAAiB;AACzD,UAAM,aAAa,OAAO,QAAQ;AAAA,MAChC,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,kBAAkB,OAAO,oBAAoB;AAAA,MACjD,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,WAAW,OAAO,IAAI,mBAAmB,UAAU,GAAG;AAAA,MAC1D,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,KAAK,WAAW;AAGlB,WAAK,UAAU,gBAAgB;AAAA,IACjC;AAGA,QAAI,KAAK,iBAAiB,6BAA6B,MAAM;AAC3D,WAAK,4BAA4B,KAAK,gBAAgB;AAAA,IACxD;AACA,SAAK,qBAAqB,IAAI,mBAAmB,0BAA0B,KAAK,WAAW,iBAAiB,YAAY,KAAK,YAAY;AACzI,SAAK,kBAAkB,KAAK,uBAAuB;AACnD,SAAK,WAAW,YAAY,OAAO,IAAI,SAAS,QAAQ,KAAK;AAE7D,SAAK,KAAK,KAAK;AAAA,EACjB;AAAA,EACA,WAAW;AACT,SAAK,kBAAkB,IAAI,eAAe,KAAK,QAAQ;AACvD,SAAK,aAAa,KAAK;AACvB,SAAK,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC1E,UAAI,KAAK,WAAW;AAClB,aAAK,gBAAgB,KAAK,iBAAiB,KAAK,uBAAuB;AACvE,aAAK,mBAAmB,cAAc;AAAA,MACxC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,aAAa,KAAK;AACvB,SAAK,aAAa,SAAS;AAC3B,SAAK,gBAAgB;AACrB,SAAK,gBAAgB,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC7E,YAAM,MAAM,QAAQ,YAAU,OAAO,OAAO,CAAC;AAC7C,YAAM,QAAQ,QAAQ,YAAU,OAAO,SAAS,CAAC;AAAA,IACnD,CAAC;AACD,SAAK,QAAQ,QAAQ,KAAK,UAAU,IAAI,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACnF,WAAK,cAAc;AACnB,WAAK,qBAAqB;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EACA,YAAY;AACV,UAAM,oBAAoB,KAAK,0BAA0B;AACzD,UAAM,YAAY,KAAK;AAIvB,QAAI,sBAAsB,KAAK,wBAAwB;AACrD,YAAM,UAAU,KAAK,YAAY;AACjC,WAAK,yBAAyB;AAC9B,UAAI,mBAAmB;AACrB,gBAAQ,aAAa,mBAAmB,iBAAiB;AAAA,MAC3D,OAAO;AACL,gBAAQ,gBAAgB,iBAAiB;AAAA,MAC3C;AAAA,IACF;AACA,QAAI,WAAW;AAEb,UAAI,KAAK,qBAAqB,UAAU,SAAS;AAC/C,YAAI,KAAK,qBAAqB,UAAa,UAAU,aAAa,QAAQ,UAAU,aAAa,KAAK,UAAU;AAC9G,eAAK,WAAW,UAAU;AAAA,QAC5B;AACA,aAAK,mBAAmB,UAAU;AAAA,MACpC;AACA,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AAGnB,QAAI,QAAQ,UAAU,KAAK,QAAQ,qBAAqB,GAAG;AACzD,WAAK,aAAa,KAAK;AAAA,IACzB;AACA,QAAI,QAAQ,2BAA2B,KAAK,KAAK,aAAa;AAC5D,WAAK,YAAY,cAAc,KAAK,yBAAyB;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB;AACtB,SAAK,aAAa,QAAQ;AAC1B,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AACvB,SAAK,aAAa,SAAS;AAC3B,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,YAAY,KAAK,MAAM,IAAI,KAAK,KAAK;AAAA,EAC5C;AAAA;AAAA,EAEA,OAAO;AACL,QAAI,CAAC,KAAK,SAAS,GAAG;AACpB;AAAA,IACF;AAIA,QAAI,KAAK,kBAAkB;AACzB,WAAK,0BAA0B,KAAK,iBAAiB,0BAA0B;AAAA,IACjF;AACA,SAAK,iBAAiB;AACtB,SAAK,gBAAgB,KAAK,iBAAiB,KAAK,uBAAuB;AACvE,SAAK,0BAA0B;AAC/B,SAAK,aAAa;AAClB,SAAK,YAAY,eAAe,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AAC5D,WAAK,mBAAmB,cAAc;AACtC,WAAK,oBAAoB;AAAA,IAC3B,CAAC;AACD,SAAK,YAAY,cAAc;AAC/B,SAAK,YAAY,0BAA0B,IAAI;AAC/C,SAAK,wBAAwB;AAC7B,SAAK,mBAAmB,aAAa;AAErC,SAAK,aAAa,KAAK;AAEvB,YAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,aAAa,KAAK,IAAI,CAAC;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBhB,4BAA4B;AAO1B,UAAM,QAAQ,KAAK,YAAY,cAAc,QAAQ,mDAAmD;AACxG,QAAI,CAAC,OAAO;AAEV;AAAA,IACF;AACA,UAAM,UAAU,GAAG,KAAK,EAAE;AAC1B,QAAI,KAAK,eAAe;AACtB,6BAAuB,KAAK,eAAe,aAAa,OAAO;AAAA,IACjE;AACA,wBAAoB,OAAO,aAAa,OAAO;AAC/C,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA,EAEA,kBAAkB;AAChB,QAAI,CAAC,KAAK,eAAe;AAEvB;AAAA,IACF;AACA,UAAM,UAAU,GAAG,KAAK,EAAE;AAC1B,2BAAuB,KAAK,eAAe,aAAa,OAAO;AAC/D,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA,EAEA,QAAQ;AACN,QAAI,KAAK,YAAY;AACnB,WAAK,aAAa;AAClB,WAAK,eAAe;AACpB,WAAK,YAAY,0BAA0B,KAAK,OAAO,IAAI,QAAQ,KAAK;AACxE,WAAK,mBAAmB,aAAa;AACrC,WAAK,WAAW;AAEhB,WAAK,aAAa,KAAK;AAEvB,cAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,aAAa,KAAK,KAAK,CAAC;AAAA,IAC5D;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,QAAI,KAAK,uBAAuB,CAAC,KAAK,OAAO;AAC3C,WAAK,eAAe;AACpB;AAAA,IACF;AACA,SAAK,iBAAiB;AACtB,SAAK,iBAAiB,MAAM;AAC1B,mBAAa;AACb,mBAAa,iBAAiB;AAC9B,WAAK,iBAAiB;AAAA,IACxB;AACA,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,eAAe,KAAK,UAAU,OAAO,OAAO,gBAAgB,WAAS;AACzE,UAAI,MAAM,kBAAkB,oBAAoB;AAC9C,aAAK,iBAAiB;AACtB,aAAK,eAAe;AAAA,MACtB;AAAA,IACF,CAAC;AAGD,UAAM,oBAAoB,WAAW,MAAM;AACzC,WAAK,iBAAiB;AACtB,WAAK,eAAe;AAAA,IACtB,GAAG,GAAG;AACN,UAAM,UAAU,IAAI,uBAAuB;AAAA,EAC7C;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,YAAY,cAAc;AAG/B,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,OAAO;AAChB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,IAAI;AACnB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAkB,IAAI;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,YAAY;AAC3B,SAAK,WAAW;AAChB,SAAK,mBAAmB,aAAa;AACrC,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,WAAW,KAAK,iBAAiB,YAAY,CAAC,IAAI,KAAK,iBAAiB,SAAS,CAAC;AAAA,EAChG;AAAA;AAAA,EAEA,IAAI,eAAe;AACjB,QAAI,KAAK,OAAO;AACd,aAAO;AAAA,IACT;AACA,QAAI,KAAK,WAAW;AAClB,YAAM,kBAAkB,KAAK,gBAAgB,SAAS,IAAI,YAAU,OAAO,SAAS;AACpF,UAAI,KAAK,OAAO,GAAG;AACjB,wBAAgB,QAAQ;AAAA,MAC1B;AAEA,aAAO,gBAAgB,KAAK,IAAI;AAAA,IAClC;AACA,WAAO,KAAK,gBAAgB,SAAS,CAAC,EAAE;AAAA,EAC1C;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,mBAAmB,iBAAiB;AAAA,EAC3C;AAAA;AAAA,EAEA,SAAS;AACP,WAAO,KAAK,OAAO,KAAK,KAAK,UAAU,QAAQ;AAAA,EACjD;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,YAAY,KAAK,mBAAmB,KAAK,IAAI,KAAK,qBAAqB,KAAK;AAAA,IACnF;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB,OAAO;AAC1B,UAAM,UAAU,MAAM;AACtB,UAAM,aAAa,YAAY,cAAc,YAAY,YAAY,YAAY,cAAc,YAAY;AAC3G,UAAM,YAAY,YAAY,SAAS,YAAY;AACnD,UAAM,UAAU,KAAK;AAErB,QAAI,CAAC,QAAQ,SAAS,KAAK,aAAa,CAAC,eAAe,KAAK,MAAM,KAAK,YAAY,MAAM,WAAW,YAAY;AAC/G,YAAM,eAAe;AACrB,WAAK,KAAK;AAAA,IACZ,WAAW,CAAC,KAAK,UAAU;AACzB,YAAM,2BAA2B,KAAK;AACtC,cAAQ,UAAU,KAAK;AACvB,YAAM,iBAAiB,KAAK;AAE5B,UAAI,kBAAkB,6BAA6B,gBAAgB;AAGjE,aAAK,eAAe,SAAS,eAAe,WAAW,GAAK;AAAA,MAC9D;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB,OAAO;AACxB,UAAM,UAAU,KAAK;AACrB,UAAM,UAAU,MAAM;AACtB,UAAM,aAAa,YAAY,cAAc,YAAY;AACzD,UAAM,WAAW,QAAQ,SAAS;AAClC,QAAI,cAAc,MAAM,QAAQ;AAE9B,YAAM,eAAe;AACrB,WAAK,MAAM;AAAA,IAGb,WAAW,CAAC,aAAa,YAAY,SAAS,YAAY,UAAU,QAAQ,cAAc,CAAC,eAAe,KAAK,GAAG;AAChH,YAAM,eAAe;AACrB,cAAQ,WAAW,sBAAsB;AAAA,IAC3C,WAAW,CAAC,YAAY,KAAK,aAAa,YAAY,KAAK,MAAM,SAAS;AACxE,YAAM,eAAe;AACrB,YAAM,uBAAuB,KAAK,QAAQ,KAAK,SAAO,CAAC,IAAI,YAAY,CAAC,IAAI,QAAQ;AACpF,WAAK,QAAQ,QAAQ,YAAU;AAC7B,YAAI,CAAC,OAAO,UAAU;AACpB,iCAAuB,OAAO,OAAO,IAAI,OAAO,SAAS;AAAA,QAC3D;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,YAAM,yBAAyB,QAAQ;AACvC,cAAQ,UAAU,KAAK;AACvB,UAAI,KAAK,aAAa,cAAc,MAAM,YAAY,QAAQ,cAAc,QAAQ,oBAAoB,wBAAwB;AAC9H,gBAAQ,WAAW,sBAAsB;AAAA,MAC3C;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,sBAAsB,OAAO;AAK3B,QAAI,MAAM,YAAY,UAAU,CAAC,eAAe,KAAK,GAAG;AACtD,YAAM,eAAe;AACrB,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA,WAAW;AACT,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW;AAChB,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,WAAW;AAChB,SAAK,aAAa,gBAAgB;AAClC,QAAI,CAAC,KAAK,YAAY,CAAC,KAAK,WAAW;AACrC,WAAK,WAAW;AAChB,WAAK,mBAAmB,aAAa;AACrC,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK,mBAAmB,OAAO,KAAK,iBAAiB,KAAK,KAAK;AAAA,EACxE;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,CAAC,KAAK,mBAAmB,KAAK,gBAAgB,QAAQ;AAAA,EAC/D;AAAA,EACA,uBAAuB;AAGrB,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,UAAI,KAAK,WAAW;AAClB,aAAK,SAAS,KAAK,UAAU;AAAA,MAC/B;AACA,WAAK,qBAAqB,KAAK,MAAM;AACrC,WAAK,aAAa,KAAK;AAAA,IACzB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,OAAO;AAC1B,SAAK,QAAQ,QAAQ,YAAU,OAAO,kBAAkB,CAAC;AACzD,SAAK,gBAAgB,MAAM;AAC3B,QAAI,KAAK,YAAY,OAAO;AAC1B,UAAI,CAAC,MAAM,QAAQ,KAAK,MAAM,OAAO,cAAc,eAAe,YAAY;AAC5E,cAAM,+BAA+B;AAAA,MACvC;AACA,YAAM,QAAQ,kBAAgB,KAAK,qBAAqB,YAAY,CAAC;AACrE,WAAK,YAAY;AAAA,IACnB,OAAO;AACL,YAAM,sBAAsB,KAAK,qBAAqB,KAAK;AAG3D,UAAI,qBAAqB;AACvB,aAAK,YAAY,iBAAiB,mBAAmB;AAAA,MACvD,WAAW,CAAC,KAAK,WAAW;AAG1B,aAAK,YAAY,iBAAiB,EAAE;AAAA,MACtC;AAAA,IACF;AACA,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,OAAO;AAC1B,UAAM,sBAAsB,KAAK,QAAQ,KAAK,YAAU;AAGtD,UAAI,KAAK,gBAAgB,WAAW,MAAM,GAAG;AAC3C,eAAO;AAAA,MACT;AACA,UAAI;AAEF,gBAAQ,OAAO,SAAS,QAAQ,KAAK,6BAA6B,KAAK,aAAa,OAAO,OAAO,KAAK;AAAA,MACzG,SAAS,OAAO;AACd,YAAI,OAAO,cAAc,eAAe,WAAW;AAEjD,kBAAQ,KAAK,KAAK;AAAA,QACpB;AACA,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,QAAI,qBAAqB;AACvB,WAAK,gBAAgB,OAAO,mBAAmB;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,aAAa,UAAU;AAErB,QAAI,aAAa,KAAK,UAAU,KAAK,aAAa,MAAM,QAAQ,QAAQ,GAAG;AACzE,UAAI,KAAK,SAAS;AAChB,aAAK,qBAAqB,QAAQ;AAAA,MACpC;AACA,WAAK,SAAS;AACd,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,iBAAiB,YAAU;AACzB,QAAI,KAAK,WAAW;AAElB,aAAO;AAAA,IACT;AAIA,WAAO,OAAO;AAAA,EAChB;AAAA;AAAA,EAEA,iBAAiB,iBAAiB;AAChC,QAAI,KAAK,eAAe,QAAQ;AAC9B,YAAM,eAAe,2BAA2B,mBAAmB,gBAAgB,aAAa,mBAAmB,KAAK;AACxH,aAAO,aAAa,cAAc,sBAAsB,EAAE;AAAA,IAC5D;AACA,WAAO,KAAK,eAAe,OAAO,KAAK,KAAK;AAAA,EAC9C;AAAA;AAAA,EAEA,wBAAwB;AACtB,QAAI,KAAK,SAAS;AAChB,iBAAW,UAAU,KAAK,SAAS;AACjC,eAAO,mBAAmB,aAAa;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,cAAc,IAAI,2BAA2B,KAAK,OAAO,EAAE,cAAc,KAAK,yBAAyB,EAAE,wBAAwB,EAAE,0BAA0B,KAAK,OAAO,IAAI,QAAQ,KAAK,EAAE,eAAe,EAAE,eAAe,EAAE,wBAAwB,CAAC,UAAU,CAAC,EAAE,cAAc,KAAK,cAAc;AAC1S,SAAK,YAAY,OAAO,UAAU,MAAM;AACtC,UAAI,KAAK,WAAW;AAGlB,YAAI,CAAC,KAAK,YAAY,KAAK,YAAY,YAAY;AACjD,eAAK,YAAY,WAAW,sBAAsB;AAAA,QACpD;AAGA,aAAK,MAAM;AACX,aAAK,MAAM;AAAA,MACb;AAAA,IACF,CAAC;AACD,SAAK,YAAY,OAAO,UAAU,MAAM;AACtC,UAAI,KAAK,cAAc,KAAK,OAAO;AACjC,aAAK,sBAAsB,KAAK,YAAY,mBAAmB,CAAC;AAAA,MAClE,WAAW,CAAC,KAAK,cAAc,CAAC,KAAK,YAAY,KAAK,YAAY,YAAY;AAC5E,aAAK,YAAY,WAAW,sBAAsB;AAAA,MACpD;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,gBAAgB;AACd,UAAM,qBAAqB,MAAM,KAAK,QAAQ,SAAS,KAAK,QAAQ;AACpE,SAAK,uBAAuB,KAAK,UAAU,kBAAkB,CAAC,EAAE,UAAU,WAAS;AACjF,WAAK,UAAU,MAAM,QAAQ,MAAM,WAAW;AAC9C,UAAI,MAAM,eAAe,CAAC,KAAK,YAAY,KAAK,YAAY;AAC1D,aAAK,MAAM;AACX,aAAK,MAAM;AAAA,MACb;AAAA,IACF,CAAC;AAGD,UAAM,GAAG,KAAK,QAAQ,IAAI,YAAU,OAAO,aAAa,CAAC,EAAE,KAAK,UAAU,kBAAkB,CAAC,EAAE,UAAU,MAAM;AAI7G,WAAK,mBAAmB,cAAc;AACtC,WAAK,aAAa,KAAK;AAAA,IACzB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,UAAU,QAAQ,aAAa;AAC7B,UAAM,cAAc,KAAK,gBAAgB,WAAW,MAAM;AAC1D,QAAI,CAAC,KAAK,4BAA4B,OAAO,SAAS,QAAQ,CAAC,KAAK,WAAW;AAC7E,aAAO,SAAS;AAChB,WAAK,gBAAgB,MAAM;AAC3B,UAAI,KAAK,SAAS,MAAM;AACtB,aAAK,kBAAkB,OAAO,KAAK;AAAA,MACrC;AAAA,IACF,OAAO;AACL,UAAI,gBAAgB,OAAO,UAAU;AACnC,eAAO,WAAW,KAAK,gBAAgB,OAAO,MAAM,IAAI,KAAK,gBAAgB,SAAS,MAAM;AAAA,MAC9F;AACA,UAAI,aAAa;AACf,aAAK,YAAY,cAAc,MAAM;AAAA,MACvC;AACA,UAAI,KAAK,UAAU;AACjB,aAAK,YAAY;AACjB,YAAI,aAAa;AAKf,eAAK,MAAM;AAAA,QACb;AAAA,MACF;AAAA,IACF;AACA,QAAI,gBAAgB,KAAK,gBAAgB,WAAW,MAAM,GAAG;AAC3D,WAAK,kBAAkB;AAAA,IACzB;AACA,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA;AAAA,EAEA,cAAc;AACZ,QAAI,KAAK,UAAU;AACjB,YAAM,UAAU,KAAK,QAAQ,QAAQ;AACrC,WAAK,gBAAgB,KAAK,CAAC,GAAG,MAAM;AAClC,eAAO,KAAK,iBAAiB,KAAK,eAAe,GAAG,GAAG,OAAO,IAAI,QAAQ,QAAQ,CAAC,IAAI,QAAQ,QAAQ,CAAC;AAAA,MAC1G,CAAC;AACD,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB,eAAe;AAC/B,QAAI;AACJ,QAAI,KAAK,UAAU;AACjB,oBAAc,KAAK,SAAS,IAAI,YAAU,OAAO,KAAK;AAAA,IACxD,OAAO;AACL,oBAAc,KAAK,WAAW,KAAK,SAAS,QAAQ;AAAA,IACtD;AACA,SAAK,SAAS;AACd,SAAK,YAAY,KAAK,WAAW;AACjC,SAAK,UAAU,WAAW;AAC1B,SAAK,gBAAgB,KAAK,KAAK,gBAAgB,WAAW,CAAC;AAC3D,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B;AACxB,QAAI,KAAK,aAAa;AACpB,UAAI,KAAK,OAAO;AAId,YAAI,0BAA0B;AAC9B,iBAAS,QAAQ,GAAG,QAAQ,KAAK,QAAQ,QAAQ,SAAS;AACxD,gBAAM,SAAS,KAAK,QAAQ,IAAI,KAAK;AACrC,cAAI,CAAC,OAAO,UAAU;AACpB,sCAA0B;AAC1B;AAAA,UACF;AAAA,QACF;AACA,aAAK,YAAY,cAAc,uBAAuB;AAAA,MACxD,OAAO;AACL,aAAK,YAAY,cAAc,KAAK,gBAAgB,SAAS,CAAC,CAAC;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,CAAC,KAAK,cAAc,CAAC,KAAK,YAAY,KAAK,SAAS,SAAS,KAAK,CAAC,CAAC,KAAK;AAAA,EAClF;AAAA;AAAA,EAEA,MAAM,SAAS;AACb,SAAK,YAAY,cAAc,MAAM,OAAO;AAAA,EAC9C;AAAA;AAAA,EAEA,0BAA0B;AACxB,QAAI,KAAK,WAAW;AAClB,aAAO;AAAA,IACT;AACA,UAAM,UAAU,KAAK,kBAAkB,WAAW,KAAK;AACvD,UAAM,kBAAkB,UAAU,UAAU,MAAM;AAClD,WAAO,KAAK,iBAAiB,kBAAkB,KAAK,iBAAiB;AAAA,EACvE;AAAA;AAAA,EAEA,2BAA2B;AACzB,QAAI,KAAK,aAAa,KAAK,eAAe,KAAK,YAAY,YAAY;AACrE,aAAO,KAAK,YAAY,WAAW;AAAA,IACrC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,4BAA4B;AAC1B,QAAI,KAAK,WAAW;AAClB,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,KAAK,kBAAkB,WAAW,KAAK;AACnD,QAAI,KAAK,gBAAgB;AACvB,eAAS,MAAM,KAAK;AAAA,IACtB;AAOA,QAAI,CAAC,OAAO;AACV,cAAQ,KAAK;AAAA,IACf;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,iBAAiB;AACnB,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,sBAAsB,QAAQ,aAAa,kBAAkB;AACnE,WAAO,qBAAqB,MAAM,GAAG,KAAK,CAAC;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,KAAK;AACrB,QAAI,IAAI,QAAQ;AACd,WAAK,YAAY,cAAc,aAAa,oBAAoB,IAAI,KAAK,GAAG,CAAC;AAAA,IAC/E,OAAO;AACL,WAAK,YAAY,cAAc,gBAAgB,kBAAkB;AAAA,IACnE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,SAAK,MAAM;AACX,SAAK,KAAK;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,mBAAmB;AAGrB,WAAO,KAAK,aAAa,CAAC,KAAK,SAAS,KAAK,WAAW,CAAC,CAAC,KAAK;AAAA,EACjE;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,IAC1B,gBAAgB,SAAS,yBAAyB,IAAI,KAAK,UAAU;AACnE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,oBAAoB,CAAC;AACjD,QAAG,eAAe,UAAU,WAAW,CAAC;AACxC,QAAG,eAAe,UAAU,cAAc,CAAC;AAAA,MAC7C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,gBAAgB,GAAG;AACpE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU;AAC3D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe;AAAA,MAClE;AAAA,IACF;AAAA,IACA,WAAW,SAAS,gBAAgB,IAAI,KAAK;AAC3C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,KAAK,CAAC;AACrB,QAAG,YAAY,qBAAqB,CAAC;AAAA,MACvC;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAC9D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAC5D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,MACpE;AAAA,IACF;AAAA,IACA,WAAW,CAAC,QAAQ,YAAY,iBAAiB,WAAW,GAAG,gBAAgB;AAAA,IAC/E,UAAU;AAAA,IACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,WAAW,SAAS,qCAAqC,QAAQ;AAC7E,iBAAO,IAAI,eAAe,MAAM;AAAA,QAClC,CAAC,EAAE,SAAS,SAAS,qCAAqC;AACxD,iBAAO,IAAI,SAAS;AAAA,QACtB,CAAC,EAAE,QAAQ,SAAS,oCAAoC;AACtD,iBAAO,IAAI,QAAQ;AAAA,QACrB,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,MAAM,IAAI,EAAE,EAAE,YAAY,IAAI,WAAW,KAAK,IAAI,QAAQ,EAAE,iBAAiB,IAAI,YAAY,IAAI,KAAK,WAAW,IAAI,EAAE,iBAAiB,IAAI,SAAS,EAAE,cAAc,IAAI,aAAa,IAAI,EAAE,iBAAiB,IAAI,SAAS,SAAS,CAAC,EAAE,iBAAiB,IAAI,SAAS,SAAS,CAAC,EAAE,gBAAgB,IAAI,UAAU,EAAE,yBAAyB,IAAI,yBAAyB,CAAC;AACtX,QAAG,YAAY,2BAA2B,IAAI,QAAQ,EAAE,0BAA0B,IAAI,UAAU,EAAE,2BAA2B,IAAI,QAAQ,EAAE,wBAAwB,IAAI,KAAK,EAAE,2BAA2B,IAAI,QAAQ;AAAA,MACvN;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,qBAAqB,CAAC,GAAG,oBAAoB,qBAAqB;AAAA,MAClE,YAAY;AAAA,MACZ,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACrE,UAAU,CAAC,GAAG,YAAY,YAAY,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK,CAAC;AAAA,MACzF,8BAA8B,CAAC,GAAG,gCAAgC,gCAAgC,gBAAgB;AAAA,MAClH,aAAa;AAAA,MACb,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,wBAAwB,CAAC,GAAG,0BAA0B,0BAA0B,gBAAgB;AAAA,MAChG,aAAa;AAAA,MACb,OAAO;AAAA,MACP,WAAW,CAAC,GAAG,cAAc,WAAW;AAAA,MACxC,gBAAgB,CAAC,GAAG,mBAAmB,gBAAgB;AAAA,MACvD,mBAAmB;AAAA,MACnB,2BAA2B,CAAC,GAAG,6BAA6B,6BAA6B,eAAe;AAAA,MACxG,gBAAgB;AAAA,MAChB,IAAI;AAAA,MACJ,YAAY;AAAA,MACZ,0BAA0B,CAAC,GAAG,4BAA4B,4BAA4B,gBAAgB;AAAA,IACxG;AAAA,IACA,SAAS;AAAA,MACP,cAAc;AAAA,MACd,eAAe;AAAA,MACf,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACf;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,IACtB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,GAAG;AAAA,MACD,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,oBAAoB;AAAA,IAC5B,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,yBAAyB,oBAAoB,WAAW,EAAE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,sBAAsB,IAAI,GAAG,0BAA0B,GAAG,OAAO,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,8BAA8B,yBAAyB,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,GAAG,8BAA8B,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,WAAW,aAAa,SAAS,QAAQ,UAAU,QAAQ,aAAa,SAAS,eAAe,MAAM,GAAG,CAAC,KAAK,gBAAgB,GAAG,CAAC,yBAAyB,IAAI,mCAAmC,IAAI,kCAAkC,IAAI,oCAAoC,oCAAoC,GAAG,UAAU,iBAAiB,kBAAkB,mCAAmC,iCAAiC,qCAAqC,6BAA6B,gCAAgC,4BAA4B,uCAAuC,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,QAAQ,WAAW,YAAY,MAAM,GAAG,WAAW,SAAS,CAAC;AAAA,IAC1gC,UAAU,SAAS,mBAAmB,IAAI,KAAK;AAC7C,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,gBAAgB,GAAG;AACtB,QAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,WAAW,SAAS,SAAS,0CAA0C;AACxE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,KAAK,CAAC;AAAA,QAClC,CAAC;AACD,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,oBAAoB,GAAG,kCAAkC,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,kCAAkC,GAAG,GAAG,QAAQ,CAAC;AACjI,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,QAAG,eAAe;AAClB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,QAAG,aAAa,EAAE,EAAE,EAAE;AACtB,QAAG,WAAW,IAAI,mCAAmC,GAAG,IAAI,eAAe,EAAE;AAC7E,QAAG,WAAW,UAAU,SAAS,oDAAoD;AACnF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,MAAM,CAAC;AAAA,QACnC,CAAC,EAAE,iBAAiB,SAAS,2DAA2D;AACtF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,MAAM,CAAC;AAAA,QACnC,CAAC,EAAE,kBAAkB,SAAS,0DAA0D,QAAQ;AAC9F,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,sBAAsB,MAAM,CAAC;AAAA,QACzD,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,cAAM,2BAA8B,YAAY,CAAC;AACjD,QAAG,UAAU,CAAC;AACd,QAAG,YAAY,MAAM,IAAI,QAAQ;AACjC,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,QAAQ,IAAI,CAAC;AAClC,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,mCAAmC,IAAI,EAAE,iCAAiC,IAAI,kBAAkB,EAAE,qCAAqC,IAAI,eAAe,EAAE,6BAA6B,IAAI,2BAA2B,wBAAwB,EAAE,gCAAgC,IAAI,UAAU,EAAE,4BAA4B,IAAI,aAAa,EAAE,yCAAyC,IAAI;AAAA,MAC9Y;AAAA,IACF;AAAA,IACA,cAAc,CAAC,kBAAkB,qBAAqB,OAAO;AAAA,IAC7D,QAAQ,CAAC,65JAA+5J;AAAA,IACx6J,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,aAAa;AAAA,QACb,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,qBAAqB;AAAA,QACrB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gCAAgC;AAAA,QAChC,mCAAmC;AAAA,QACnC,kCAAkC;AAAA,QAClC,mCAAmC;AAAA,QACnC,gCAAgC;AAAA,QAChC,mCAAmC;AAAA,QACnC,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,GAAG;AAAA,QACD,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,SAAS,CAAC,kBAAkB,qBAAqB,OAAO;AAAA,MACxD,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,65JAA+5J;AAAA,IAC16J,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,QACnB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK;AAAA,MAC/D,CAAC;AAAA,IACH,CAAC;AAAA,IACD,8BAA8B,CAAC;AAAA,MAC7B,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAIH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,IAClC,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,eAAe,iBAAiB,iBAAiB,WAAW,gBAAgB;AAAA,IACtF,SAAS,CAAC,qBAAqB,oBAAoB,WAAW,kBAAkB,iBAAiB,eAAe;AAAA,EAClH,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,mCAAmC;AAAA,IAC/C,SAAS,CAAC,eAAe,iBAAiB,iBAAiB,qBAAqB,oBAAoB,iBAAiB,eAAe;AAAA,EACtI,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,eAAe,iBAAiB,iBAAiB,WAAW,gBAAgB;AAAA,MACtF,SAAS,CAAC,qBAAqB,oBAAoB,WAAW,kBAAkB,iBAAiB,eAAe;AAAA,MAChH,WAAW,CAAC,mCAAmC;AAAA,IACjD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACj+CH,IAAM,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBxB,gBAAgB;AAAA,IACZ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACT;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ,EAAE,SAAS,GAAG,WAAW,gBAAgB;AAAA,UACjD,QAAQ;AAAA,QACZ;AAAA,MACJ;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,WAAW;AAAA,UACP,MAAM;AAAA,UACN,QAAQ;AAAA,YACJ,MAAM;AAAA,YACN,QAAQ,EAAE,SAAS,GAAG,WAAW,cAAc;AAAA,YAC/C,QAAQ;AAAA,UACZ;AAAA,UACA,SAAS;AAAA,QACb;AAAA,QACA,SAAS;AAAA,MACb;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,WAAW;AAAA,UACP,MAAM;AAAA,UACN,QAAQ,EAAE,MAAM,GAAG,QAAQ,EAAE,SAAS,EAAE,GAAG,QAAQ,KAAK;AAAA,UACxD,SAAS;AAAA,QACb;AAAA,QACA,SAAS;AAAA,MACb;AAAA,IACJ;AAAA,IACA,SAAS,CAAC;AAAA,EACd;AACJ;", "names": []}