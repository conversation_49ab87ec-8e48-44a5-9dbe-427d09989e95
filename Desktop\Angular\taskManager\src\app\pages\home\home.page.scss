:host {
    display: block;
    padding: 24px;
    background: #ffffff;
    color: #000000;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h1 {
        margin: 0;
        font-weight: 700;
        color: #000000;
    }
}

.status-cards {
    display: grid;
    grid-template-columns: repeat(3, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;

    .stat {
        background: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: 16px;
        padding: 16px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, .06);

        .top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #4b5563;
        }

        .num {
            font-size: 32px;
            font-weight: 700;
            margin-top: 8px;
            color: #000000;
        }
    }
}

.create {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 16px;
    padding: 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, .06);

    h2 {
        margin-top: 0;
        color: #000000;
    }

    .row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
    }

    .block {
        width: 100%;
    }

    .w {
        width: 100%;
    }

    .actions {
        display: flex;
        gap: 12px;
        margin-top: 8px;
    }
}