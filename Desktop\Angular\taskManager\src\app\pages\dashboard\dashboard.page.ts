import { ChangeDetectionStrategy, Component, computed, inject, signal } from '@angular/core';
import { RouterLink } from '@angular/router';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { TaskService } from '../../services/task.service';
import { Task, TaskPriority, TaskStatus } from '../../models/task.model';
import { TaskCardComponent } from '../../components/task-card/task-card.component';

type SortKey = 'due_asc' | 'due_desc' | 'title_asc';

@Component({
    selector: 'tm-dashboard',
    standalone: true,
    imports: [RouterLink, MatSelectModule, MatInputModule, MatIconModule, MatButtonModule, FormsModule, TaskCardComponent],
    templateUrl: './dashboard.page.html',
    styleUrls: ['./dashboard.page.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class DashboardPage {
    private store = inject(TaskService);
    tasks = this.store.tasks;

    // filters
    q = signal('');
    status = signal<'all' | TaskStatus>('all');
    priority = signal<'all' | TaskPriority>('all');
    sort = signal<SortKey>('due_asc');

    totals = computed(() => ({
        total: this.tasks().length,
        pending: this.tasks().filter(t => t.status === 'pending').length,
        inProgress: this.tasks().filter(t => t.status === 'in_progress').length,
        completed: this.tasks().filter(t => t.status === 'completed').length,
    }));

    filtered = computed(() => {
        const q = this.q().toLowerCase().trim();
        const s = this.status(), p = this.priority(), sort = this.sort();
        let arr = this.tasks().filter(t =>
            (!q || t.title.toLowerCase().includes(q) || t.description?.toLowerCase().includes(q)) &&
            (s === 'all' || t.status === s) &&
            (p === 'all' || t.priority === p)
        );
        arr = [...arr].sort((a: Task, b: Task) => {
            if (sort === 'title_asc') return a.title.localeCompare(b.title);
            const da = new Date(a.dueDate).getTime();
            const db = new Date(b.dueDate).getTime();
            return sort === 'due_asc' ? da - db : db - da;
        });
        return arr;
    });

    remove(id: string) { this.store.remove(id); }
}
