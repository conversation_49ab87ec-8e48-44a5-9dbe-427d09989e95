:host {
    display: block;
    padding: 24px;
    background: #f7f8fb;
}

.topbar {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;

    .left {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .stat-cards {
        display: grid;
        grid-template-columns: repeat(4, minmax(140px, 1fr));
        gap: 12px;

        .mini {
            background: #fff;
            border-radius: 16px;
            padding: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, .06);

            .k {
                color: #6b7280;
                font-size: 12px;
            }

            .v {
                font-size: 24px;
                font-weight: 700;
            }
        }
    }
}

.filters {
    display: grid;
    grid-template-columns: 1.5fr 1fr 1fr 1fr;
    gap: 12px;
    margin-bottom: 14px;

    .q {
        width: 100%;
    }
}

.list {
    display: grid;
    gap: 12px;
}

.empty {
    display: flex;
    gap: 8px;
    align-items: center;
    color: #6b7280;
    padding: 12px;
}