import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { ReactiveFormsModule, FormBuilder, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { RouterLink } from '@angular/router';
// import { v4 as uuid } from 'uuid';
import { TaskService } from '../../services/task.service';
import { Task } from '../../models/task.model';

function futureDateValidator(c: AbstractControl): ValidationErrors | null {
    const v = c.value as Date | null; if (!v) return null;
    const today = new Date(); today.setHours(0, 0, 0, 0);
    return v < today ? { pastDate: true } : null;
}

@Component({
    selector: 'tm-home',
    standalone: true,
    imports: [
        ReactiveFormsModule, MatFormFieldModule, MatInputModule, MatDatepickerModule,
        MatNativeDateModule, MatSelectModule, MatButtonModule, MatIconModule, RouterLink
    ],
    templateUrl: './home.page.html',
    styleUrls: ['./home.page.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class HomePage {
    private fb = inject(FormBuilder);
    private store = inject(TaskService);

    tasks = this.store.tasks;
    todayCount = computed(() => this.tasks().filter(t => new Date(t.dueDate).toDateString() === new Date().toDateString()).length);
    inProgress = computed(() => this.tasks().filter(t => t.status === 'in_progress').length);
    completed = computed(() => this.tasks().filter(t => t.status === 'completed').length);

    form = this.fb.nonNullable.group({
        title: ['', [Validators.required, Validators.minLength(3)]],
        dueDate: [null as Date | null, [Validators.required, futureDateValidator]],
        description: [''],
        priority: ['medium' as 'low' | 'medium' | 'high', [Validators.required]]
    });

    submit() {
        if (this.form.invalid) { this.form.markAllAsTouched(); return; }
        const f = this.form.getRawValue();
        const task: Task = {
            id: crypto.randomUUID(),
            title: f.title.trim(),
            description: f.description?.trim(),
            dueDate: f.dueDate!.toISOString(),
            priority: f.priority,
            status: 'pending',
            createdAt: new Date().toISOString()
        };
        this.store.add(task);
        this.form.reset({ title: '', description: '', dueDate: null, priority: 'medium' });
    }
}
