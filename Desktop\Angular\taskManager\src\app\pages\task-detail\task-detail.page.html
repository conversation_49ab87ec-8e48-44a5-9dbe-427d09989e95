<div class="header">
  <button mat-icon-button (click)="back()" aria-label="back">
    <mat-icon>arrow_back</mat-icon>
  </button>
  <div class="titles">
    <h1>Task Details</h1>
    <p>View and edit task information</p>
  </div>

  <div class="header-actions">
    <button mat-stroked-button (click)="startEdit()" [disabled]="editing() || !task()">
      <mat-icon>edit</mat-icon> Edit Task
    </button>
    <button mat-flat-button color="primary" (click)="markComplete()" [disabled]="!task() || task()?.status==='completed'">
      <mat-icon>check_circle</mat-icon> Mark Complete
    </button>
  </div>
</div>

@if (!task()) {
  <div class="empty">
    <mat-icon>error_outline</mat-icon>
    <p>Task not found.</p>
  </div>
} @else {
  <div class="grid">
    <!-- Left: main info -->
    <section class="panel">
      <h3>Task Information</h3>

      @if (!editing()) {
        <div class="read">
          <h2 class="title">{{ task()!.title }}</h2>
          <div class="chips">
            <span class="chip priority {{task()!.priority}}">{{ task()!.priority }} priority</span>
            <span class="chip status" [class.pending]="task()!.status==='pending'"
                                      [class.in]="task()!.status==='in_progress'"
                                      [class.done]="task()!.status==='completed'">
              {{ task()!.status.replace('_',' ') }}
            </span>
          </div>

          <div class="block">
            <label>Description</label>
            <p class="desc">{{ task()!.description || '—' }}</p>
          </div>

          <div class="meta-line">
            <mat-icon>event</mat-icon>
            <span>Due: {{ task()!.dueDate | date:'fullDate' }}</span>
          </div>
        </div>
      } @else {
        <form [formGroup]="form" (ngSubmit)="save()" class="edit">
          <mat-form-field appearance="fill" class="w">
            <mat-label>Title</mat-label>
            <input matInput formControlName="title" placeholder="Enter task title...">
            @if (form.controls.title.touched && form.controls.title.invalid) {
              <mat-error>Title (min 3) required</mat-error>
            }
          </mat-form-field>

          <mat-form-field appearance="fill" class="w">
            <mat-label>Description</mat-label>
            <textarea placeholder="Add task description..." matInput rows="4" formControlName="description"></textarea>
          </mat-form-field>

          <div class="row">
            <mat-form-field appearance="fill" class="w">
              <mat-label>Priority</mat-label>
              <mat-select formControlName="priority">
                <mat-option value="low">Low</mat-option>
                <mat-option value="medium">Medium</mat-option>
                <mat-option value="high">High</mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="fill" class="w">
              <mat-label>Status</mat-label>
              <mat-select formControlName="status">
                <mat-option value="pending">Pending</mat-option>
                <mat-option value="in_progress">In Progress</mat-option>
                <mat-option value="completed">Completed</mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="fill" class="w">
              <mat-label>Due Date</mat-label>
              <input placeholder="" matInput [matDatepicker]="picker" formControlName="dueDate">
              <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </div>

          <div class="actions">
            <button mat-stroked-button type="button" (click)="cancelEdit()">
              <mat-icon>close</mat-icon> Cancel
            </button>
            <button mat-flat-button color="primary" type="submit">
              <mat-icon>save</mat-icon> Save Changes
            </button>
          </div>
        </form>
      }
    </section>

    <!-- Right: quick actions & metadata -->
    <aside class="side">
      <section class="panel">
        <h3>Quick Actions</h3>
        <div class="stack">
          <button mat-flat-button color="primary" (click)="markComplete()" [disabled]="task()?.status==='completed'">
            <mat-icon>check_circle</mat-icon> Mark Complete
          </button>
          <button mat-stroked-button (click)="startEdit()">
            <mat-icon>edit</mat-icon> Edit Task
          </button>
        </div>
      </section>

      <section class="panel">
        <h3>Task Metadata</h3>
        <div class="meta">
          <div><span>Created</span><b>{{ task()!.createdAt | date:'fullDate' }}</b></div>
          <div><span>Last Updated</span><b>{{ task()!.updatedAt || task()!.createdAt | date:'fullDate' }}</b></div>
          <div><span>Task ID</span><b>{{ task()!.id }}</b></div>
        </div>
      </section>
    </aside>
  </div>

  <section class="danger panel">
    <h3>Danger Zone</h3>
    <div class="dz">
      <div>
        <b>Delete this task permanently</b>
        <p>This action cannot be undone</p>
      </div>
      <button mat-flat-button color="warn" (click)="deleteTask()">
        <mat-icon>delete</mat-icon> Delete Task
      </button>
    </div>
  </section>
}
