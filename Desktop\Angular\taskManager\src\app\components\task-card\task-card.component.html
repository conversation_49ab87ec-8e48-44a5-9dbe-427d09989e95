<div class="card">
  <div class="heading">
    <h3><a [routerLink]="['/task', task.id]">{{ task.title }}</a></h3>
    <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="task actions">
      <mat-icon>more_vert</mat-icon>
    </button>
    <mat-menu #menu="matMenu">
      <button mat-menu-item [routerLink]="['/task', task.id]">
        <mat-icon>open_in_new</mat-icon> Open
      </button>
      <button mat-menu-item (click)="delete.emit(task.id)">
        <mat-icon>delete</mat-icon> Delete
      </button>
    </mat-menu>
  </div>

  @if (task.description) {
    <p class="desc">{{ task.description }}</p>
  }

  <div class="tags">
    <span class="chip status" [class.pending]="task.status==='pending'"
                               [class.in]="task.status==='in_progress'"
                               [class.done]="task.status==='completed'">
      {{ task.status.replace('_',' ') }}
    </span>
    <span class="chip priority {{task.priority}}">{{ task.priority }}</span>
    <span class="due"><mat-icon>event</mat-icon> Due: {{ task.dueDate | date:'MMM d, y' }}</span>
  </div>
</div>
