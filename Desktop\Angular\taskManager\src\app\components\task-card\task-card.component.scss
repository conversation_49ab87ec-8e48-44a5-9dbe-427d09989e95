.card {
    background: #fff;
    border-radius: 16px;
    padding: 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, .06);

    .heading {
        display: flex;
        align-items: center;
        gap: 8px;

        h3 {
            flex: 1;
            margin: 0;
            font-weight: 600;
        }
    }

    .desc {
        margin: 6px 0 10px;
        color: #5b6470;
    }

    .tags {
        display: flex;
        align-items: center;
        gap: 10px;
        flex-wrap: wrap;

        .chip {
            padding: 2px 10px;
            border-radius: 999px;
            font-size: 12px;
            text-transform: capitalize;
        }

        .priority.low {
            background: #edf7ff
        }

        .priority.medium {
            background: #f5f2ff
        }

        .priority.high {
            background: #ffebee
        }

        .status.pending {
            background: #fff7e6
        }

        .status.in {
            background: #e8f5ff
        }

        .status.done {
            background: #eafbea
        }

        .due {
            display: flex;
            align-items: center;
            gap: 4px;
            color: #6b7280;
        }
    }
}