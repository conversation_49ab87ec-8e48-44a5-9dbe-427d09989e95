import { ChangeDetectionStrategy, Component, computed, inject, signal } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { TaskService } from '../../services/task.service';
import { Task, TaskPriority, TaskStatus } from '../../models/task.model';
import { ReactiveFormsModule, FormBuilder, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'tm-task-detail',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule,
        MatSelectModule, MatDatepickerModule, MatNativeDateModule
    ],

    templateUrl: './task-detail.page.html',
    styleUrls: ['./task-detail.page.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class TaskDetailPage {
    private route = inject(ActivatedRoute);
    private router = inject(Router);
    private store = inject(TaskService);
    private fb = inject(FormBuilder);

    private id = this.route.snapshot.paramMap.get('id')!;
    task = computed<Task | null>(() => this.store.tasks().find(t => t.id === this.id) ?? null);

    editing = signal(false);

    form = this.fb.nonNullable.group({
        title: ['', [Validators.required, Validators.minLength(3)]],
        description: [''],
        priority: ['medium' as TaskPriority, Validators.required],
        status: ['pending' as TaskStatus, Validators.required],
        dueDate: [null as Date | null, Validators.required]
    });

    constructor() {
        const t = this.task();
        if (t) {
            this.form.reset({
                title: t.title,
                description: t.description ?? '',
                priority: t.priority,
                status: t.status,
                dueDate: new Date(t.dueDate)
            });
        }
    }

    back() { this.router.navigateByUrl('/dashboard'); }

    startEdit() { this.editing.set(true); }
    cancelEdit() {
        const t = this.task(); if (!t) return;
        this.form.reset({
            title: t.title,
            description: t.description ?? '',
            priority: t.priority,
            status: t.status,
            dueDate: new Date(t.dueDate)
        });
        this.editing.set(false);
    }

    save() {
        const t = this.task(); if (!t || this.form.invalid) { this.form.markAllAsTouched(); return; }
        const v = this.form.getRawValue();
        const updated: Task = {
            ...t,
            title: v.title.trim(),
            description: v.description?.trim(),
            priority: v.priority,
            status: v.status,
            dueDate: v.dueDate!.toISOString()
        };
        this.store.update(updated);
        this.editing.set(false);
    }

    markComplete() {
        const t = this.task(); if (!t) return;
        this.store.setStatus(t.id, 'completed');
    }

    deleteTask() {
        const t = this.task(); if (!t) return;
        if (confirm('Delete this task permanently?')) {
            this.store.remove(t.id);
            this.router.navigateByUrl('/dashboard');
        }
    }
}
